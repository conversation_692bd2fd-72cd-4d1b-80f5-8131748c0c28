# 前端组件架构

**文档版本**: v1.0  
**所属模块**: AI富文本编辑器架构  
**最后更新**: 2025-08-22

## 2. 前端组件架构

### 2.1 组件层次结构

```
src/views/ai-rich-editor/
├── components/                  # 业务组件
│   ├── RichEditor/
│   │   ├── index.vue           # 主编辑器组件
│   │   ├── EditorToolbar.vue   # 工具栏组件
│   │   ├── EditorContent.vue   # 编辑区域组件
│   │   └── EditorStatus.vue    # 状态栏组件
│   ├── AIAssistant/
│   │   ├── index.vue           # AI助手主组件
│   │   ├── ModelSelector.vue   # 模型选择器
│   │   ├── PromptInput.vue     # 指令输入组件
│   │   └── ResponseViewer.vue  # 响应预览组件
│   ├── FileManager/
│   │   ├── index.vue           # 文件管理主组件
│   │   ├── UploadZone.vue      # 上传区域
│   │   ├── FileList.vue        # 文件列表
│   │   └── PreviewModal.vue    # 预览模态框
│   └── Common/
│       ├── LoadingSpinner.vue  # 加载动画
│       ├── ErrorBoundary.vue   # 错误边界
│       └── Notification.vue    # 通知组件
├── services/                   # 业务服务
│   ├── editor.service.ts       # 编辑器核心服务
│   ├── ai.service.ts          # AI集成服务
│   ├── file.service.ts        # 文件处理服务
│   └── storage.service.ts     # 存储服务
├── composables/               # 组合式函数
│   ├── useEditor.ts           # 编辑器逻辑
│   ├── useAI.ts              # AI功能逻辑
│   ├── useFile.ts            # 文件操作逻辑
│   └── useSecurity.ts        # 安全相关逻辑
├── types/                     # 类型定义
│   ├── editor.types.ts        # 编辑器类型
│   ├── ai.types.ts           # AI相关类型
│   └── file.types.ts         # 文件相关类型
└── utils/                     # 工具函数
    ├── editor.utils.ts        # 编辑器工具
    ├── ai.utils.ts           # AI工具函数
    └── validation.utils.ts   # 验证工具
```

### 2.2 核心组件设计

#### 2.2.1 主编辑器组件 (RichEditor/index.vue)

```typescript
<template>
  <div class="rich-editor-container">
    <EditorToolbar 
      :editor="editor"
      @command="handleCommand"
    />
    <div class="editor-main">
      <EditorContent 
        :content="content"
        @update:content="updateContent"
        @selection-change="handleSelectionChange"
      />
      <AIAssistant 
        v-if="showAI"
        :selection="currentSelection"
        :models="availableModels"
        @ai-command="handleAICommand"
      />
    </div>
    <EditorStatus 
      :status="editorStatus"
      :word-count="wordCount"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useEditor } from '../composables/useEditor'
import { useAI } from '../composables/useAI'
import { useFile } from '../composables/useFile'

// 组合式状态管理
const { 
  editor, 
  content, 
  currentSelection, 
  wordCount, 
  editorStatus,
  initEditor,
  updateContent,
  handleSelectionChange 
} = useEditor()

const { 
  showAI, 
  availableModels, 
  handleAICommand 
} = useAI()

const { handleFileOperation } = useFile()

// 命令处理
const handleCommand = (command: string, ...args: any[]) => {
  switch (command) {
    case 'upload-word':
      handleFileOperation('upload', 'word')
      break
    case 'ai-assist':
      showAI.value = true
      break
    default:
      editor.value?.execCommand(command, ...args)
  }
}

// 生命周期
onMounted(() => {
  initEditor()
})

onUnmounted(() => {
  editor.value?.destroy()
})
</script>
```

#### 2.2.2 AI助手组件 (AIAssistant/index.vue)

```typescript
<template>
  <div class="ai-assistant" v-if="isVisible">
    <ModelSelector 
      v-model="selectedModel"
      :models="models"
      @change="handleModelChange"
    />
    <PromptInput 
      v-model="prompt"
      :placeholder="promptPlaceholder"
      @submit="handleSubmit"
    />
    <ResponseViewer 
      v-if="response"
      :response="response"
      :loading="isLoading"
      @accept="handleAccept"
      @reject="handleReject"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useAI } from '../composables/useAI'

const props = defineProps<{
  selection: Selection | null
  models: AIModel[]
}>()

const emit = defineEmits<{
  aiCommand: [command: string, data: any]
}>()

const { 
  selectedModel, 
  prompt, 
  response, 
  isLoading,
  processText,
  cancelRequest 
} = useAI()

const promptPlaceholder = computed(() => {
  const length = props.selection?.toString().length || 0
  return length > 0 
    ? `对选中的${length}个字符进行优化...`
    : '请输入AI编辑指令...'
})

const handleSubmit = async () => {
  if (!props.selection || !prompt.value.trim()) return
  
  try {
    const result = await processText(
      props.selection.toString(),
      selectedModel.value,
      prompt.value
    )
    response.value = result
  } catch (error) {
    console.error('AI处理失败:', error)
  }
}

const handleAccept = () => {
  emit('aiCommand', 'replace-selection', response.value)
  reset()
}

const handleReject = () => {
  reset()
}

const reset = () => {
  prompt.value = ''
  response.value = ''
  isLoading.value = false
}
</script>
```