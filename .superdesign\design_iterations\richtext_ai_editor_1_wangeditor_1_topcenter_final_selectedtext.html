<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI富文本编辑器 - wangeditor风格+AI编辑+上传Word（弹窗显示选中文字）</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:400,500,700&display=swap">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.0.0/dist/flowbite.min.js"></script>
  <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
  <style>
    body { font-family: Inter, var(--font-sans), sans-serif !important; background: #f6f7f9 !important; color: #222 !important; }
    .editor-toolbar-wang { background: #fff; border-bottom: 1px solid #e5e7eb; padding: 0.5em 1em; display: flex; flex-wrap: wrap; gap: 0.5em; align-items: center; font-size: 15px; min-height: 48px; }
    .editor-toolbar-wang .group { display: flex; align-items: center; gap: 0.25em; margin-right: 1.5em; }
    .editor-toolbar-wang select, .editor-toolbar-wang button { background: transparent; border: none; color: #222; font-size: 15px; padding: 0.25em 0.5em; border-radius: 4px; cursor: pointer; transition: background 0.15s; outline: none; min-width: 32px; min-height: 32px; display: flex; align-items: center; gap: 2px; }
    .editor-toolbar-wang button:hover, .editor-toolbar-wang select:hover { background: #f3f4f6; }
    #uploadBtn { background: #2563eb !important; color: #fff !important; border: none !important; border-radius: 4px !important; font-weight: 500; box-shadow: 0 2px 8px 0 rgba(56,189,248,0.08); transition: background 0.15s, box-shadow 0.15s; min-width: 40px; min-height: 32px; padding: 0.25em 1em; margin-right: 1em; display: flex; align-items: center; gap: 4px; }
    #uploadBtn:hover { background: #1d4ed8 !important; box-shadow: 0 4px 16px 0 rgba(56,189,248,0.16); }
    .editor-wang { background: #fff; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 6px 6px; min-height: 220px; padding: 1.5em 1em 1em 1em; font-size: 16px; line-height: 1.7; color: #222; outline: none; resize: vertical; position: relative; }
    .editor-wang:empty:before { content: '请输入内容'; color: #bdbdbd; font-size: 16px; pointer-events: none; position: absolute; }
    .editor-wang[contenteditable="true"]:focus { box-shadow: 0 0 0 2px #2563eb22; }
    .icon-btn { width: 20px; height: 20px; display: inline-flex; align-items: center; justify-content: center; }
    .ai-btn { background: #2563eb !important; color: #fff !important; border-radius: 6px !important; box-shadow: 0 2px 8px 0 rgba(56,189,248,0.08); transition: all 0.2s cubic-bezier(.4,0,.2,1); opacity: 0; pointer-events: none; position: absolute; z-index: 10; padding: 0.5em 1em; font-weight: 500; font-size: 1rem; border: none; cursor: pointer; visibility: hidden; }
    .ai-btn.visible { opacity: 1; pointer-events: auto; visibility: visible; }
    .ai-btn:hover { background: #1d4ed8 !important; transform: scale(1.08); box-shadow: 0 8px 24px rgba(56,189,248,0.12); }
    .ai-modal { background: #fff !important; color: #222 !important; border-radius: 8px !important; box-shadow: 0 8px 32px rgba(56,189,248,0.10); border: 1px solid #e5e7eb !important; padding: 2em 1.5em; min-width: 320px; max-width: 90vw; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%) scale(1); opacity: 0; pointer-events: none; transition: all 0.25s cubic-bezier(.4,0,.2,1); z-index: 50; }
    .ai-modal.visible { opacity: 1; pointer-events: auto; transform: translate(-50%, -50%) scale(1); }
    .ai-modal .modal-header { font-size: 1.1rem; font-weight: 600; margin-bottom: 1em; display: flex; align-items: center; gap: 0.5em; }
    .ai-modal .selected-text-preview {
      background: #f3f4f6;
      color: #222;
      border-radius: 5px;
      padding: 0.75em 1em;
      margin-bottom: 1em;
      font-size: 1rem;
      word-break: break-all;
      border: 1px solid #e5e7eb;
      max-height: 120px;
      overflow-y: auto;
    }
    .ai-modal textarea { width: 100%; border: 1px solid #e5e7eb; border-radius: 6px; padding: 0.75em; font-size: 1rem; margin-bottom: 1em; background: #f6f7f9; color: #222; outline: none; transition: box-shadow 0.2s; }
    .ai-modal textarea:focus { box-shadow: 0 0 0 2px #2563eb44; border-color: #2563eb; }
    .ai-modal .modal-actions { display: flex; gap: 1em; justify-content: flex-end; }
    .ai-modal .btn { padding: 0.5em 1.2em; border-radius: 6px; font-weight: 500; font-size: 1rem; border: none; cursor: pointer; transition: all 0.15s; }
    .ai-modal .btn-primary { background: #2563eb; color: #fff; }
    .ai-modal .btn-secondary { background: #e5e7eb; color: #222; }
    .ai-modal .btn-primary:disabled { opacity: 0.6; cursor: not-allowed; }
    .ai-modal .loading-spinner { display: inline-block; width: 1.5em; height: 1.5em; border: 3px solid #e5e7eb; border-top: 3px solid #2563eb; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 0.5em; }
    @keyframes spin { to { transform: rotate(360deg); } }
    .highlighted { background: #38bdf8 !important; transition: background 0.3s; animation: highlight-fade 2s forwards; }
    @keyframes highlight-fade { 0% { background: #38bdf8; } 80% { background: #38bdf8; } 100% { background: transparent; } }
  </style>
</head>
<body>
  <div class="max-w-3xl mx-auto mt-10">
    <div class="editor-toolbar-wang rounded-t-md">
      <button id="uploadBtn" type="button">
        <i data-lucide="file-plus" class="icon-btn"></i> 上传Word
      </button>
      <div class="group">
        <select><option>正文</option><option>标题1</option><option>标题2</option></select>
      </div>
      <div class="group">
        <button title="引用"><i data-lucide="quote" class="icon-btn"></i></button>
        <button title="加粗"><i data-lucide="bold" class="icon-btn"></i></button>
        <button title="斜体"><i data-lucide="italic" class="icon-btn"></i></button>
        <button title="下划线"><i data-lucide="underline" class="icon-btn"></i></button>
        <button title="更多"><i data-lucide="more-horizontal" class="icon-btn"></i></button>
        <button title="字号"><span style="font-size:13px;">A▼</span></button>
      </div>
      <div class="group">
        <button title="默认字号">默认字号</button>
        <button title="默认字体">默认字体</button>
        <button title="默认行高">默认行高</button>
      </div>
      <div class="group">
        <button title="表格"><i data-lucide="table" class="icon-btn"></i></button>
        <button title="代码"><i data-lucide="code" class="icon-btn"></i></button>
        <button title="撤销"><i data-lucide="rotate-ccw" class="icon-btn"></i></button>
        <button title="重做"><i data-lucide="rotate-cw" class="icon-btn"></i></button>
        <button title="全屏"><i data-lucide="maximize" class="icon-btn"></i></button>
      </div>
      <div class="group">
        <button title="有序列表"><i data-lucide="list" class="icon-btn"></i></button>
        <button title="无序列表"><i data-lucide="list" class="icon-btn"></i></button>
        <button title="对齐"><i data-lucide="align-left" class="icon-btn"></i></button>
      </div>
      <div class="group">
        <button title="表情"><i data-lucide="smile" class="icon-btn"></i></button>
        <button title="链接"><i data-lucide="link" class="icon-btn"></i></button>
        <button title="图片"><i data-lucide="image" class="icon-btn"></i></button>
      </div>
    </div>
    <div class="editor-wang" id="editorWang" contenteditable="true" spellcheck="true"></div>
    <button id="aiEditBtn" class="ai-btn">AI编辑</button>
    <div id="aiModal" class="ai-modal">
      <div class="modal-header">
        <i data-lucide="sparkles"></i> AI编辑
      </div>
      <div id="selectedTextPreview" class="selected-text-preview" style="display:none;"></div>
      <textarea id="aiPrompt" rows="3" placeholder="请输入您的AI指令，如：润色这段话、改写为更正式的语气……"></textarea>
      <div class="modal-actions">
        <button id="aiSubmit" class="btn btn-primary">提交</button>
        <button id="aiCancel" class="btn btn-secondary">取消</button>
      </div>
      <div id="aiLoading" style="display:none;margin-top:1em;"><span class="loading-spinner"></span>AI处理中…</div>
      <div id="aiError" style="display:none;color:#e11d48;margin-top:1em;">AI处理失败，请重试。</div>
    </div>
  </div>
  <input type="file" id="wordInput" accept=".doc,.docx" style="display:none;">
  <script>
    lucide.createIcons();
    // AI编辑按钮浮现逻辑（正上方精确居中，且只在选中文字时出现）
    const editor = document.getElementById('editorWang');
    const aiBtn = document.getElementById('aiEditBtn');
    let selectionRange = null;
    let lastSelectedText = '';
    function updateAiBtn() {
      const sel = window.getSelection();
      if (
        sel && sel.rangeCount > 0 &&
        !sel.isCollapsed &&
        editor.contains(sel.anchorNode) &&
        editor.contains(sel.focusNode)
      ) {
        selectionRange = sel.getRangeAt(0);
        const rect = selectionRange.getBoundingClientRect();
        const editorRect = editor.getBoundingClientRect();
        // 先让按钮可见但透明，获取宽高
        aiBtn.classList.add('visible');
        aiBtn.style.opacity = '0';
        aiBtn.style.pointerEvents = 'none';
        aiBtn.style.visibility = 'hidden';
        requestAnimationFrame(() => {
          const btnWidth = aiBtn.offsetWidth;
          const btnHeight = aiBtn.offsetHeight;
          // 精确居中于选区正上方
          aiBtn.style.top = (rect.top - editorRect.top + window.scrollY - btnHeight - 8) + 'px';
          aiBtn.style.left = (rect.left - editorRect.left + window.scrollX + (rect.width / 2) - (btnWidth / 2)) + 'px';
          aiBtn.style.opacity = '1';
          aiBtn.style.pointerEvents = 'auto';
          aiBtn.style.visibility = 'visible';
        });
        // 记录选中的文本
        lastSelectedText = sel.toString();
      } else {
        aiBtn.classList.remove('visible');
        aiBtn.style.opacity = '0';
        aiBtn.style.pointerEvents = 'none';
        aiBtn.style.visibility = 'hidden';
      }
    }
    document.addEventListener('selectionchange', updateAiBtn);
    document.addEventListener('scroll', updateAiBtn, true);
    window.addEventListener('resize', updateAiBtn);
    document.addEventListener('mousedown', function(e) {
      if (!aiBtn.contains(e.target) && !editor.contains(e.target)) {
        aiBtn.classList.remove('visible');
        aiBtn.style.opacity = '0';
        aiBtn.style.pointerEvents = 'none';
        aiBtn.style.visibility = 'hidden';
      }
    });
    // AI编辑弹窗逻辑
    const aiModal = document.getElementById('aiModal');
    const aiPrompt = document.getElementById('aiPrompt');
    const aiSubmit = document.getElementById('aiSubmit');
    const aiCancel = document.getElementById('aiCancel');
    const aiLoading = document.getElementById('aiLoading');
    const aiError = document.getElementById('aiError');
    const selectedTextPreview = document.getElementById('selectedTextPreview');
    aiBtn.addEventListener('click', function() {
      aiModal.classList.add('visible');
      aiPrompt.value = '';
      aiError.style.display = 'none';
      aiLoading.style.display = 'none';
      // 显示选中的文本
      if (lastSelectedText && lastSelectedText.trim()) {
        selectedTextPreview.textContent = lastSelectedText;
        selectedTextPreview.style.display = 'block';
      } else {
        selectedTextPreview.textContent = '';
        selectedTextPreview.style.display = 'none';
      }
      aiPrompt.focus();
    });
    aiCancel.addEventListener('click', function() {
      aiModal.classList.remove('visible');
    });
    aiSubmit.addEventListener('click', function() {
      if (!aiPrompt.value.trim()) return;
      aiLoading.style.display = 'block';
      aiError.style.display = 'none';
      aiSubmit.disabled = true;
      // 模拟AI接口调用
      setTimeout(() => {
        aiLoading.style.display = 'none';
        aiSubmit.disabled = false;
        aiModal.classList.remove('visible');
        if (selectionRange) {
          // 模拟AI返回内容
          const newText = aiPrompt.value + '（AI已处理）';
          selectionRange.deleteContents();
          const span = document.createElement('span');
          span.className = 'highlighted';
          span.textContent = newText;
          selectionRange.insertNode(span);
          setTimeout(() => {
            span.classList.remove('highlighted');
          }, 2000);
        }
        aiBtn.classList.remove('visible');
        aiBtn.style.opacity = '0';
        aiBtn.style.pointerEvents = 'none';
        aiBtn.style.visibility = 'hidden';
      }, 1800);
    });
    // 上传Word逻辑（仅模拟）
    const uploadBtn = document.getElementById('uploadBtn');
    const wordInput = document.getElementById('wordInput');
    uploadBtn.addEventListener('click', () => wordInput.click());
    wordInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        editor.innerHTML = `<h2>${file.name}</h2><p>（Word内容解析后显示在此）</p>`;
      }
    });
  </script>
</body>
</html>
