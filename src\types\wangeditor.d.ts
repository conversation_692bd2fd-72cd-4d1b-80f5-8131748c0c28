declare module '@wangeditor/editor' {
  interface IDomEditor {
    getHtml: () => string
    getSelectionText: () => string
    deleteFragment: () => void
    insertText: (text: string) => void
    setHtml: (html: string) => void
    destroy: () => void
    on: (event: string, callback: Function) => void
  }

  interface IEditorConfig {
    placeholder: string
    readOnly: boolean
    autoFocus: boolean
    scroll: boolean
    maxLength: number
    [key: string]: any
  }

  interface IToolbarConfig {
    excludeKeys: string[]
    [key: string]: any
  }

  export function createEditor(options: {
    selector: HTMLElement
    config?: Partial<IEditorConfig>
    html?: string
    mode: 'default' | 'simple'
  }): IDomEditor

  export function createToolbar(options: {
    editor: IDomEditor
    selector: HTMLElement
    config?: Partial<IToolbarConfig>
    mode: 'default' | 'simple'
  }): void
}
