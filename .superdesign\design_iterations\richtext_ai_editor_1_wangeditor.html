<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI富文本编辑器 - wangeditor风格</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:400,500,700&display=swap">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.0.0/dist/flowbite.min.js"></script>
  <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
  <style>
    body {
      font-family: Inter, var(--font-sans), sans-serif !important;
      background: #f6f7f9 !important;
      color: #222 !important;
    }
    .editor-toolbar-wang {
      background: #fff;
      border-bottom: 1px solid #e5e7eb;
      padding: 0.5em 1em;
      display: flex;
      flex-wrap: wrap;
      gap: 0.5em;
      align-items: center;
      font-size: 15px;
      min-height: 48px;
    }
    .editor-toolbar-wang .group {
      display: flex;
      align-items: center;
      gap: 0.25em;
      margin-right: 1.5em;
    }
    .editor-toolbar-wang select,
    .editor-toolbar-wang button {
      background: transparent;
      border: none;
      color: #222;
      font-size: 15px;
      padding: 0.25em 0.5em;
      border-radius: 4px;
      cursor: pointer;
      transition: background 0.15s;
      outline: none;
      min-width: 32px;
      min-height: 32px;
      display: flex;
      align-items: center;
      gap: 2px;
    }
    .editor-toolbar-wang button:hover,
    .editor-toolbar-wang select:hover {
      background: #f3f4f6;
    }
    .editor-toolbar-wang .divider {
      width: 1px;
      height: 24px;
      background: #e5e7eb;
      margin: 0 0.5em;
    }
    .editor-wang {
      background: #fff;
      border: 1px solid #e5e7eb;
      border-top: none;
      border-radius: 0 0 6px 6px;
      min-height: 220px;
      padding: 1.5em 1em 1em 1em;
      font-size: 16px;
      line-height: 1.7;
      color: #222;
      outline: none;
      resize: vertical;
    }
    .editor-wang:empty:before {
      content: '请输入内容';
      color: #bdbdbd;
      font-size: 16px;
      pointer-events: none;
      position: absolute;
    }
    .editor-wang[contenteditable="true"]:focus {
      box-shadow: 0 0 0 2px #2563eb22;
    }
    .icon-btn {
      width: 20px;
      height: 20px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  </style>
</head>
<body>
  <div class="max-w-3xl mx-auto mt-10">
    <div class="editor-toolbar-wang rounded-t-md">
      <div class="group">
        <select><option>正文</option><option>标题1</option><option>标题2</option></select>
      </div>
      <div class="group">
        <button title="引用"><i data-lucide="quote" class="icon-btn"></i></button>
        <button title="加粗"><i data-lucide="bold" class="icon-btn"></i></button>
        <button title="斜体"><i data-lucide="italic" class="icon-btn"></i></button>
        <button title="下划线"><i data-lucide="underline" class="icon-btn"></i></button>
        <button title="更多"><i data-lucide="more-horizontal" class="icon-btn"></i></button>
        <button title="字号"><span style="font-size:13px;">A▼</span></button>
      </div>
      <div class="group">
        <button title="默认字号">默认字号</button>
        <button title="默认字体">默认字体</button>
        <button title="默认行高">默认行高</button>
      </div>
      <div class="group">
        <button title="表格"><i data-lucide="table" class="icon-btn"></i></button>
        <button title="代码"><i data-lucide="code" class="icon-btn"></i></button>
        <button title="撤销"><i data-lucide="rotate-ccw" class="icon-btn"></i></button>
        <button title="重做"><i data-lucide="rotate-cw" class="icon-btn"></i></button>
        <button title="全屏"><i data-lucide="maximize" class="icon-btn"></i></button>
      </div>
      <div class="group">
        <button title="有序列表"><i data-lucide="list" class="icon-btn"></i></button>
        <button title="无序列表"><i data-lucide="list" class="icon-btn"></i></button>
        <button title="对齐"><i data-lucide="align-left" class="icon-btn"></i></button>
      </div>
      <div class="group">
        <button title="表情"><i data-lucide="smile" class="icon-btn"></i></button>
        <button title="链接"><i data-lucide="link" class="icon-btn"></i></button>
        <button title="图片"><i data-lucide="image" class="icon-btn"></i></button>
      </div>
    </div>
    <div class="editor-wang" id="editorWang" contenteditable="true" spellcheck="true"></div>
  </div>
  <script>
    lucide.createIcons();
    // 可扩展：工具栏按钮功能、下拉菜单等
  </script>
</body>
</html>
