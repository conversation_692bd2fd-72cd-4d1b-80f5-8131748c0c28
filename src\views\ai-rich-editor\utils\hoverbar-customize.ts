// 关于 wangEditor 中的 hoverbarKeys 的自定义扩展
import { IButtonMenu, IDomEditor } from '@wangeditor/editor';
import { h } from 'vue'
import AIPolishModalComp from '../components/AIPolishModal.vue'

// AI润色
class AIPolishMenu implements IButtonMenu {
  constructor() {
    this.title = 'AI润色' // 自定义菜单标题
    this.tag = 'button'
  }
  title: string
  iconSvg?: string | undefined
  hotkey?: string | undefined
  alwaysEnable?: boolean | undefined
  tag: string
  width?: number | undefined

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue(editor: IDomEditor): string | boolean {
    return ''
  }

  isActive(editor: IDomEditor): boolean {                  // JS 语法
    return false
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled(editor: IDomEditor): boolean {                    
    return false
  }

  // 点击菜单时触发的函数
  exec(editor: IDomEditor, value: string | boolean) {
    h(AIPolishModalComp)
  }
}


export const hoverbar = {
  key: 'hoverbar-customize', // 定义 menu key ：要保证唯一、不重复（重要）
  factory() {
    return new AIPolishMenu() // 把 `YourMenuClass` 替换为你菜单的 class
  },
}
