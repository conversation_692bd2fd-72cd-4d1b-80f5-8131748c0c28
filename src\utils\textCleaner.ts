/**
 * 文本清理工具
 * 用于清理HTML、Markdown、Word等格式中的样式信息
 */

export interface CleanOptions {
  removeHtmlTags?: boolean
  removeStyles?: boolean
  normalizeWhitespace?: boolean
  preserveParagraphs?: boolean
  removeMarkdown?: boolean
  removeEmptyLines?: boolean
}

const defaultOptions: CleanOptions = {
  removeHtmlTags: true,
  removeStyles: true,
  normalizeWhitespace: true,
  preserveParagraphs: true,
  removeMarkdown: true
}

/**
 * 清理HTML标签
 */
function cleanHtmlTags(text: string): string {
  // 移除所有HTML标签，但保留换行相关的标签
  return text
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
    .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<\/p>/gi, '\n\n')
    .replace(/<p[^>]*>/gi, '')
    .replace(/<div[^>]*>/gi, '')
    .replace(/<\/div>/gi, '\n')
    .replace(/<[^>]+>/g, '')
}

/**
 * 清理样式属性
 */
function cleanStyles(text: string): string {
  // 移除内联样式和类属性
  return text
    .replace(/style="[^"]*"/gi, '')
    .replace(/class="[^"]*"/gi, '')
    .replace(/id="[^"]*"/gi, '')
    .replace(/color="[^"]*"/gi, '')
    .replace(/size="[^"]*"/gi, '')
    .replace(/face="[^"]*"/gi, '')
}

/**
 * 规范化空白字符
 */
function normalizeWhitespace(text: string, removeEmptyLines: boolean = false): string {
  if (!removeEmptyLines) {
    return text
  }
  let result = text
    .replace(/\r\n/g, '\n') // 统一换行符
    .replace(/\r/g, '\n')
    .replace(/\t/g, ' ') // 制表符转空格
    .replace(/[ \u00A0\u2000-\u200B\u202F\u205F\u3000]+/g, ' ') // 合并空白字符
  
  
  return result.trim()
}

/**
 * 清理HTML实体
 */
function cleanHtmlEntities(text: string): string {
  const entities: Record<string, string> = {
    '&nbsp;': ' ',
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'",
    '&hellip;': '...',
    '&mdash;': '—',
    '&ndash;': '–'
  }

  return Object.entries(entities).reduce(
    (text, [entity, replacement]) => text.replace(new RegExp(entity, 'g'), replacement),
    text
  )
}

/**
 * 主要清理函数
 */
export function cleanText(text: string, options: CleanOptions = {}): string {
  const opts = { ...defaultOptions, ...options }
  let cleaned = text
  // 先清理样式，再清理HTML标签
  if (opts.removeStyles) {
    cleaned = cleanStyles(cleaned)
  }
  
  if (opts.removeHtmlTags) {
    cleaned = cleanHtmlTags(cleaned)
  }
  
  cleaned = cleanHtmlEntities(cleaned)
  
  // 清理Markdown语法
  cleaned = cleanMarkdownSyntax(cleaned, opts.removeMarkdown);
  
  if (opts.normalizeWhitespace) {
    cleaned = normalizeWhitespace(cleaned, opts.removeEmptyLines)
  }
  
  return cleaned
}

/**
 * 清理Markdown语法
 */
function cleanMarkdownSyntax(text: string, removeMarkdown: boolean = false): string {
  if (!removeMarkdown) {
    return text;
  }
  let result = text
    // 清理标题标记 (# ## ### 等)
    .replace(/^[#]{1,6}\s+/gm, '')
    // 清理强调标记 (* ** _ __)
    .replace(/(\*\*|__)(.*?)\1/g, '$2')
    .replace(/(\*|_)(.*?)\1/g, '$2')
    // 清理链接 [text](url) -> text
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // 清理图片 ![alt](url) -> alt
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1')
    // 清理代码标记 (` ````)
    .replace(/`{1,3}([^`]+)`{1,3}/g, '$1')
    .replace(/```[\s\S]*?```/g, '')
    // 清理列表标记 (- * + 1. 2. 等)
    .replace(/^[\s]*[-*+]\s+/gm, '')
    .replace(/^[\s]*\d+\.\s+/gm, '')
    // 清理引用标记 (>)
    .replace(/^[\s]*>[\s]?/gm, '')
    // 清理表格标记 - 改进的表格处理逻辑
    .replace(/^\|[\s\S]*?\|$/gm, match => {
      // 移除表格行中的 | 分隔符，但保留内容
      return match.replace(/\|/g, '').trim();
    })
    // 清理表格分隔线行 (| --- | --- |)
    .replace(/^\|[\s]*[-:]+[\s]*\|[\s]*[-:]+[\s]*\|.*$/gm, '')
    // 清理简单的分隔线
    .replace(/^\s*[-:|]+\s*$/gm, '')
    // 清理水平线 (--- *** ___)
    .replace(/^[\s]*[-*_]{3,}[\s]*$/gm, '');
  return result.trim();
}

/**
 * 检测文本类型
 */
export function detectTextType(text: string): 'html' | 'markdown' | 'plain' {
  if (/<[^>]+>/.test(text)) {
    return 'html'
  }
  
  if (/[#*`_~\[\]]/.test(text)) {
    return 'markdown'
  }
  
  return 'plain'
}