import type { PolishRequest, PolishResponse } from '../types/ai.types'

/**
 * AI服务提供商封装类
 * 封装与api.agicto.cn/v1的交互逻辑
 */
export class AgictoAIProvider {
  private baseURL = 'https://api.agicto.cn/v1'
  private API_KEY = import.meta.env.VITE_AGICTO_API_KEY

  /**
   * 文本润色方法
   */
  async polishText(request: PolishRequest): Promise<PolishResponse> {
    const startTime = Date.now()
    
    try {
      const prompt = this.buildPolishPrompt(request.text, request.style, request.customPrompt)
      
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.API_KEY}`
        },
        body: JSON.stringify({
          model: request.model || 'gpt-3.5-turbo',
          messages: [{
            role: 'user',
            content: prompt
          }],
          max_tokens: request.maxTokens || 1000,
          temperature: 0.7,
          stream: false
        })
      })

      if (!response.ok) {
        throw new Error(`API调用失败: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      const polishedText = data.choices[0]?.message?.content?.trim() || request.text
      
      return {
        polishedText,
        originalText: request.text,
        processingTime: Date.now() - startTime,
        tokensUsed: data.usage?.total_tokens || 0,
        metadata: {
          model: data.model || request.model || 'gpt-3.5-turbo',
          processingTime: Date.now() - startTime,
          tokensUsed: data.usage?.total_tokens || 0
        }
      }
    } catch (error) {
      console.error('AI文本润色失败:', error)
      throw new Error(`文本润色失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 构建润色提示词
   */
  private buildPolishPrompt(text: string, style: string, customPrompt?: string): string {
    if (customPrompt) {
      return `${customPrompt}\n\n原文：${text}\n\n要求：保留核心信息，仅优化表达方式。`
    }

    const stylePrompts: Record<string, string> = {
      formal: '请将以下文本润色为更正式的商务语气，使用专业礼貌的表达方式：',
      concise: '请将以下文本简化为简洁明了的表达，去除冗余词语：',
      friendly: '请将以下文本调整为友好亲切的语气，适合日常交流：',
      professional: '请将以下文本优化为专业严谨的表达方式，适合技术或学术场景：',
      creative: '请将以下文本改写为更有创意的表达方式，增加生动性：'
    }

    return `${stylePrompts[style] || stylePrompts.formal}\n\n原文：${text}\n\n要求：保留核心信息，仅优化表达方式，不添加新内容。`
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseURL}/models`, {
        headers: {
          'Authorization': `Bearer ${this.API_KEY}`
        }
      })
      return response.ok
    } catch {
      return false
    }
  }
}

// 单例实例
export const aiProvider = new AgictoAIProvider()