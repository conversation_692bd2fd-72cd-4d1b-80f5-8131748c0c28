import mammoth from 'mammoth'

/**
 * Word文档解析服务类
 * 用于解析.docx和.doc格式的Word文档
 */
export class WordParser {
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
  private readonly ALLOWED_TYPES = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword'
  ]

  /**
   * 解析Word文档
   * @param file - 要解析的Word文件
   * @returns 解析后的HTML内容和纯文本内容
   */
  async parseWordDocument(file: File): Promise<{ html: string; text: string }> {
    this.validateFile(file)
    
    const arrayBuffer = await file.arrayBuffer()
    const result = await mammoth.convertToHtml({ arrayBuffer }, {
      styleMap: [
        "p[style-name='Heading 1'] => h1",
        "p[style-name='Heading 2'] => h2", 
        "p[style-name='Heading 3'] => h3",
        "p[style-name='Heading 4'] => h4",
        "p[style-name='Heading 5'] => h5",
        "p[style-name='Heading 6'] => h6",
        "p => p",
        "r[style-name='Strong'] => strong",
        "r[style-name='Emphasis'] => em"
      ]
    })
    
    if (result.messages && result.messages.length > 0) {
      console.warn('Word文档解析警告:', result.messages)
    }
    
    return {
      html: result.value,
      text: result.value.replace(/<[^>]*>/g, '').trim()
    }
  }

  /**
   * 验证文件格式和大小
   * @param file - 要验证的文件
   */
  validateFile(file: File): void {
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      throw new Error('请上传 .docx 或 .doc 格式的Word文档')
    }
    
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error('文件大小不能超过10MB')
    }
  }

  /**
   * 检查浏览器是否支持File API
   * @returns 是否支持
   */
  isFileAPISupported(): boolean {
    return !!(window.File && window.FileReader && window.FileList && window.Blob)
  }

  /**
   * 获取文件扩展名
   * @param filename - 文件名
   * @returns 扩展名
   */
  getFileExtension(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || ''
  }

  /**
   * 检查是否为支持的Word文档格式
   * @param filename - 文件名
   * @returns 是否支持
   */
  isSupportedWordFormat(filename: string): boolean {
    const ext = this.getFileExtension(filename)
    return ['docx', 'doc'].includes(ext)
  }
}