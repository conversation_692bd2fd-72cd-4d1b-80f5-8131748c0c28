<template>
  <el-dialog
    v-model="visible"
    title="AI编写"
    width="600px"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    class="ai-write-modal"
  >
    <template #header>
      <div class="modal-header">
        <el-icon class="header-icon"><Cpu /></el-icon>
        <span>AI编写</span>
      </div>
    </template>

    <!-- 选中文本预览 -->
    <div v-if="selectedText" class="selected-text-preview">
      <div class="preview-label">选中的文本：</div>
      <div class="preview-content">{{ selectedText }}</div>
    </div>

    <!-- AI指令输入 -->
    <div class="prompt-section">
      <div class="prompt-label">AI指令：</div>
      <el-input
        v-model="prompt"
        type="textarea"
        :rows="4"
        placeholder="请输入您的AI指令，如：润色这段话、改写为更正式的语气、扩展内容、总结要点……"
        :disabled="isProcessing"
        class="prompt-input"
      />
    </div>

    <!-- 模型选择 -->
    <div class="model-section">
      <div class="model-label">选择AI模型：</div>
      <el-select v-model="selectedModel" placeholder="选择模型" :disabled="isProcessing">
        <el-option
          v-for="model in availableModels"
          :key="model.id"
          :label="model.name"
          :value="model.id"
          :disabled="!model.isActive"
        >
          <div class="model-option">
            <span class="model-name">{{ model.name }}</span>
            <el-tag :type="getCategoryColor(model.category)" size="small">
              {{ getCategoryLabel(model.category) }}
            </el-tag>
          </div>
        </el-option>
      </el-select>
    </div>

    <!-- 加载状态 -->
    <div v-if="isProcessing" class="loading-section">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>AI处理中，请稍候...</span>
    </div>

    <!-- 错误信息 -->
    <el-alert
      v-if="errorMessage"
      :title="errorMessage"
      type="error"
      :closable="false"
      class="error-alert"
    />

    <!-- 结果预览 -->
    <div v-if="result" class="result-section">
      <div class="result-label">AI生成结果：</div>
      <div class="result-content">{{ result }}</div>
      <div class="result-actions">
        <el-button type="primary" @click="applyResult" :disabled="isProcessing">
          应用结果
        </el-button>
        <el-button @click="regenerate" :disabled="isProcessing">
          重新生成
        </el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="isProcessing">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :disabled="!prompt.trim() || isProcessing"
          :loading="isProcessing"
        >
          {{ result ? '重新生成' : '生成' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Cpu, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useAIStore } from '../stores/ai'
import type { ModelCategory } from '../types/ai.types'

interface Props {
  modelValue: boolean
  selectedText?: string
}

interface Emits {
  'update:modelValue': [value: boolean]
  'write-complete': [result: string]
}

const props = withDefaults(defineProps<Props>(), {
  selectedText: ''
})

const emit = defineEmits<Emits>()

const aiStore = useAIStore()

// 状态
const prompt = ref('')
const selectedModel = ref('gpt-3.5-turbo')
const isProcessing = ref(false)
const errorMessage = ref('')
const result = ref('')

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const availableModels = computed(() => aiStore.availableModels)

// 方法
const getCategoryLabel = (category: ModelCategory) => {
  return aiStore.getModelCategoryLabel(category)
}

const getCategoryColor = (category: ModelCategory) => {
  return aiStore.getModelCategoryColor(category)
}

const handleSubmit = async () => {
  if (!prompt.value.trim()) {
    ElMessage.warning('请输入AI指令')
    return
  }

  isProcessing.value = true
  errorMessage.value = ''
  result.value = ''

  try {
    // 构建完整的提示词
    const fullPrompt = props.selectedText 
      ? `${prompt.value}\n\n原文：${props.selectedText}`
      : prompt.value

    const response = await aiStore.polishText(
      fullPrompt,
      'custom',
      prompt.value
    )

    result.value = response.polishedText
    ElMessage.success('AI生成完成')
  } catch (error) {
    errorMessage.value = error instanceof Error ? error.message : 'AI处理失败，请重试'
    ElMessage.error('AI处理失败')
  } finally {
    isProcessing.value = false
  }
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const applyResult = () => {
  if (result.value) {
    emit('write-complete', result.value)
    visible.value = false
    resetForm()
    ElMessage.success('结果已应用')
  }
}

const regenerate = () => {
  handleSubmit()
}

const resetForm = () => {
  prompt.value = ''
  result.value = ''
  errorMessage.value = ''
  isProcessing.value = false
}

// 监听弹窗打开，重置表单
watch(visible, (newValue) => {
  if (newValue) {
    resetForm()
    selectedModel.value = aiStore.currentModel
  }
})

// 监听模型变化
watch(selectedModel, (newModel) => {
  aiStore.setCurrentModel(newModel)
})
</script>

<style scoped>
.ai-write-modal :deep(.el-dialog__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-icon {
  color: var(--el-color-primary);
}

.selected-text-preview {
  margin-bottom: 20px;
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-primary);
}

.preview-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 6px;
}

.preview-content {
  font-size: 14px;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  max-height: 100px;
  overflow-y: auto;
}

.prompt-section {
  margin-bottom: 20px;
}

.prompt-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.prompt-input {
  width: 100%;
}

.model-section {
  margin-bottom: 20px;
}

.model-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.model-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.model-name {
  flex: 1;
}

.loading-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
  margin-bottom: 16px;
  color: var(--el-color-primary);
}

.error-alert {
  margin-bottom: 16px;
}

.result-section {
  margin-bottom: 20px;
}

.result-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.result-content {
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-success);
  font-size: 14px;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 12px;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-write-modal :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .result-actions {
    flex-direction: column;
  }
  
  .dialog-footer {
    flex-direction: column-reverse;
  }
}
</style>
