<template>
  <div class="ai-rich-editor-page">
    <div class="editor-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>AI富文本编辑器</h1>
        <p class="subtitle">智能写作助手，提升文档质量</p>
      </div>

      <!-- 操作栏 -->
      <div class="action-bar">
        <div class="left-actions">
          <el-button type="primary" @click="handleNewDocument">
            <el-icon><Plus /></el-icon>
            新建文档
          </el-button>
          <WordImportButton 
            @import-success="handleWordImportSuccess"
            @import-error="handleWordImportError"
            ref="wordImportRef"
          />
        </div>

        <div class="right-actions">
          <!-- AI功能按钮组 -->
          <el-button-group class="ai-actions">
            <el-button
              type="primary"
              @click="handleAIPolish"
              :disabled="!selectedText"
              size="small"
            >
              AI润色
            </el-button>
            <el-button
              @click="handleAIExpand"
              :disabled="!selectedText"
              size="small"
            >
              扩写
            </el-button>
            <el-button
              @click="handleAISummarize"
              :disabled="!selectedText"
              size="small"
            >
              总结
            </el-button>
            <el-button
              type="success"
              @click="handleAIWriteFromToolbar"
              :disabled="!selectedText"
              size="small"
            >
              <el-icon><Cpu /></el-icon>
              AI编写
            </el-button>
          </el-button-group>

          <ModelSelector />
          <el-button @click="handleExport" :disabled="!editorContent">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button @click="handleSave" type="success" :disabled="!isDirty">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
        </div>
      </div>

      <!-- 编辑器主体 -->
      <div class="editor-wrapper">
        <RichEditor
          ref="richEditorRef"
          v-model="editorContent"
          @selection-change="(selection) => handleSelectionChange(selection)"
          @ai-write-request="handleWriteRequest"
          @ready="handleEditorReady"
          :height="editorHeight"
          class="rich-editor"
        />
      </div>

      <!-- 状态栏 -->
      <div class="status-bar" v-if="editorStats">
        <div class="stats">
          <span>字数: {{ editorStats.wordCount }}</span>
          <span>字符数: {{ editorStats.charCount }}</span>
          <span v-if="editorStats.lastSaved">最后保存: {{ formatTime(editorStats.lastSaved) }}</span>
        </div>
      </div>

      <!-- 文本选择工具栏 -->
      <TextSelectionToolbar
        :selected-text="selectedText"
        :visible="showToolbar"
        :is-processing="isProcessing"
        @polish-request="handlePolishRequest"
        @ai-write-request="handleWriteRequest"
        @close="showToolbar = false"
        ref="toolbarRef"
      />

      <!-- AI润色模态框 -->
      <AIPolishModal
        v-model="showPolishModal"
        :selected-text="selectedText"
        @polish-complete="handlePolishComplete"
      />

      <!-- AI编写模态框 -->
      <AIWriteModal
        v-model="showWriteModal"
        :selected-text="selectedText"
        @write-complete="handleWriteComplete"
      />

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-overlay">
        <el-loading :loading="loading" text="加载中..." />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css'
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Plus, Download, Check, Cpu } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import RichEditor from './components/RichEditor.vue'

import AIPolishModal from './components/AIPolishModal.vue'
import AIWriteModal from './components/AIWriteModal.vue'
import WordImportButton from './components/WordImportButton.vue'
import ModelSelector from './components/ModelSelector.vue'
import { useTextSelection } from './utils/selection-utils'

// 状态管理
const editorContent = ref('')
const selectedText = ref('')
const showToolbar = ref(false)
const showPolishModal = ref(false)
const showWriteModal = ref(false)
const isProcessing = ref(false)
const loading = ref(false)
const isDirty = ref(false)
const lastSaved = ref<Date | null>(null)

// 编辑器引用
const richEditorRef = ref<InstanceType<typeof RichEditor>>()
const toolbarRef = ref<InstanceType<typeof TextSelectionToolbar>>()
const wordImportRef = ref<InstanceType<typeof WordImportButton>>()

// 使用文本选择工具
useTextSelection()

// 编辑器高度
const editorHeight = computed(() => {
  const viewportHeight = window.innerHeight
  return `${viewportHeight - 300}px`
})

// 编辑器统计信息
const editorStats = computed(() => {
  if (!editorContent.value) return null
  
  const text = editorContent.value.replace(/<[^>]*>/g, '')
  return {
    wordCount: text.trim() ? text.trim().split(/\s+/).length : 0,
    charCount: text.length,
    lastSaved: lastSaved.value
  }
})

// 处理文本选择变化
const handleSelectionChange = (selection: string) => {
  console.log('主页面接收到选择变化:', selection) // 调试信息
  // 更新选中文本
  selectedText.value = selection
  // 只有在有选中文本时显示工具栏
  showToolbar.value = !!selection.trim()
  console.log('工具栏显示状态:', showToolbar.value) // 调试信息
}

// 处理编辑器就绪
const handleEditorReady = () => {
  console.log('编辑器已就绪')
  loading.value = false
}

// 处理文本润色请求
const handlePolishRequest = (text: string) => {
  selectedText.value = text
  showToolbar.value = false
  showPolishModal.value = true
}

// 处理AI编写请求
const handleWriteRequest = (text: string) => {
  selectedText.value = text
  showToolbar.value = false
  showWriteModal.value = true
}

// 处理润色完成
const handlePolishComplete = (polishedText: string) => {
  if (richEditorRef.value) {
    richEditorRef.value.replaceSelection(polishedText)
    isDirty.value = true
    ElMessage.success('文本已更新')
  }
}

// 处理AI编写完成
const handleWriteComplete = (writeText: string) => {
  if (richEditorRef.value) {
    richEditorRef.value.replaceSelection(writeText)
    isDirty.value = true
    ElMessage.success('AI编写内容已应用')
  }
}

// 工具栏AI功能处理
const handleAIPolish = () => {
  if (!selectedText.value) {
    ElMessage.warning('请先选择要润色的文本')
    return
  }
  showPolishModal.value = true
}

const handleAIExpand = () => {
  if (!selectedText.value) {
    ElMessage.warning('请先选择要扩写的文本')
    return
  }
  // TODO: 实现扩写功能
  ElMessage.info('扩写功能开发中')
}

const handleAISummarize = () => {
  if (!selectedText.value) {
    ElMessage.warning('请先选择要总结的文本')
    return
  }
  // TODO: 实现总结功能
  ElMessage.info('总结功能开发中')
}

const handleAIWriteFromToolbar = () => {
  if (!selectedText.value) {
    ElMessage.warning('请先选择文本作为AI编写的参考')
    return
  }
  showWriteModal.value = true
}

// 新建文档
const handleNewDocument = () => {
  if (isDirty.value) {
    ElMessageBox.confirm(
      '当前文档有未保存的更改，是否继续？',
      '确认',
      {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      createNewDocument()
    })
  } else {
    createNewDocument()
  }
}

const createNewDocument = () => {
  // 清除编辑器内容
  editorContent.value = ''
  isDirty.value = false
  lastSaved.value = null
  // 清除本地存储
  localStorage.removeItem('ai-rich-editor-last')
  
  // 调用编辑器实例的方法清除内容
  if (richEditorRef.value) {
    richEditorRef.value.setContent('')
  }
  
  // 更新页面标题
  document.title = '未命名文档 - AI富文本编辑器'
  
  ElMessage.success('已创建新文档')
}

// Word文档导入成功处理
const handleWordImportSuccess = (content: { html: string; text: string }, file: File) => {
  if (isDirty.value) {
    ElMessageBox.confirm(
      '当前文档有未保存的更改，是否继续导入？',
      '确认导入',
      {
        confirmButtonText: '继续导入',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      loadWordContent(content.html, file.name)
    })
  } else {
    loadWordContent(content.html, file.name)
  }
}

const handleWordImportError = (error: Error) => {
  ElMessage.error(`导入失败: ${error.message}`)
}

const loadWordContent = (content: string, fileName: string) => {
  try {
    // 先更新编辑器内容
    if (richEditorRef.value) {
      richEditorRef.value.setContent(content)
    }
    // 然后更新绑定值
    editorContent.value = content
    isDirty.value = true
    ElMessage.success(`文档 ${fileName} 导入成功`)
  } catch (error) {
    ElMessage.error('加载文档内容失败')
    console.error('加载文档内容失败:', error)
  }
}

// 导出文档
const handleExport = () => {
  if (!editorContent.value) {
    ElMessage.warning('没有内容可导出')
    return
  }

  try {
    // 导出为HTML
    const blob = new Blob([editorContent.value], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ai-document-${new Date().toISOString().split('T')[0]}.html`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('文档已导出')
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('导出失败:', error)
  }
}

// 保存文档
const handleSave = () => {
  if (!editorContent.value) {
    ElMessage.warning('没有内容可保存')
    return
  }

  try {
    // 模拟保存到本地存储
    const document = {
      content: editorContent.value,
      savedAt: new Date().toISOString(),
      title: extractTitle()
    }
    
    localStorage.setItem('ai-rich-editor-last', JSON.stringify(document))
    lastSaved.value = new Date()
    isDirty.value = false
    ElMessage.success('文档已保存')
  } catch (error) {
    ElMessage.error('保存失败')
    console.error('保存失败:', error)
  }
}

// 提取标题
const extractTitle = () => {
  const match = editorContent.value.match(/<h1[^>]*>([^<]+)<\/h1>/)
  return match ? match[1] : '未命名文档'
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 监听内容变化
const handleContentChange = () => {
  isDirty.value = true
}

// 键盘快捷键
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 's':
        event.preventDefault()
        handleSave()
        break
      case 'n':
        event.preventDefault()
        handleNewDocument()
        break
    }
  }
}

// 页面标题
const updatePageTitle = () => {
  document.title = `${extractTitle()} - AI富文本编辑器`
}

watch(editorContent, updatePageTitle)

// 添加新的处理方法（预留给未来功能）
// const handleExpandRequest = (text: string) => {
//   selectedText.value = text
//   showToolbar.value = false
//   // TODO: 实现扩写功能
//   ElMessage.info('扩写功能开发中')
// }

// const handleSummarizeRequest = (text: string) => {
//   selectedText.value = text
//   showToolbar.value = false
//   // TODO: 实现总结功能
//   ElMessage.info('总结功能开发中')
// }

// 页面状态管理
onMounted(() => {
  loading.value = true

  // 立即关闭加载状态，因为编辑器会异步初始化
  nextTick(() => {
    loading.value = false
  })

  // 从本地存储恢复上次编辑的内容
  try {
    const saved = localStorage.getItem('ai-rich-editor-last')
    if (saved) {
      const document = JSON.parse(saved)
      editorContent.value = document.content
      lastSaved.value = new Date(document.savedAt)
    }
  } catch (error) {
    console.error('恢复文档失败:', error)
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown)

  // 监听内容变化
  watch(editorContent, handleContentChange, { deep: true })

  // 初始化页面标题
  updatePageTitle()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.ai-rich-editor-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.editor-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.page-header {
  padding: 32px 24px 24px;
  text-align: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
}

.subtitle {
  margin: 0;
  font-size: 16px;
  color: #7f8c8d;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.left-actions,
.right-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.ai-actions {
  margin-right: 16px;
}

.ai-actions .el-button {
  border-radius: 4px;
}

.ai-actions .el-button:disabled {
  opacity: 0.5;
}

.upload-btn :deep(.el-upload) {
  display: inline-block;
}

.editor-wrapper {
  padding: 20px;
  background: #fff;
}

/* 编辑器核心样式 */
.rich-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.rich-editor :deep(.w-e-text-container) {
  background-color: #fff;
}

.rich-editor :deep(.w-e-toolbar) {
  border-bottom: 1px solid #dcdfe6;
  background-color: #fafafa;
}

.rich-editor :deep(.w-e-text-placeholder) {
  color: #999;
}

/* 确保编辑区域最小高度 */
.rich-editor :deep(.w-e-text-container [data-slate-editor]) {
  min-height: 300px;
  padding: 0 10px;
}

.status-bar {
  padding: 12px 24px;
  border-top: 1px solid #e4e7ed;
  background: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #606266;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-rich-editor-page {
    padding: 0;
  }
  
  .editor-container {
    border-radius: 0;
    min-height: 100vh;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
  
  .action-bar {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .left-actions,
  .right-actions {
    justify-content: center;
  }
  
  .stats {
    flex-direction: column;
    gap: 4px;
  }
}
</style>