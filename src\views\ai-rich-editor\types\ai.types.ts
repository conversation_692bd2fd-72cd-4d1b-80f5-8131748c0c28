/**
 * AI相关类型定义
 * 多模型AI选择器完整类型定义
 */

// AI 润色样式类型
export interface PolishStyle {
  value: string
  label: string
  description: string
  icon: string
}

// AI 模型类型
export interface AIModel {
  id: string
  name: string
  description: string
  category: ModelCategory
  responseTime: string
  qualityRating: number
  bestFor: string[]
  isActive: boolean
  maxTokens: number
  pricePerToken: number
}

export type ModelCategory = 'basic' | 'advanced' | 'specialized' | 'speed' | 'quality' | 'chinese' | 'multilingual'

export interface ModelRecommendationContext {
  contentType: string
  textLength: number
  urgency: 'high' | 'medium' | 'low'
  qualityRequirement: 'high' | 'medium' | 'low'
  language: string
  useCase: string
}

// AI 请求参数类型
export interface PolishRequest {
  text: string
  style: string
  customPrompt?: string
  model: string
  maxTokens?: number
}

// AI 响应类型
export interface PolishResponse {
  polishedText: string
  originalText: string
  processingTime: number
  tokensUsed?: number
  metadata?: {
    model: string
    processingTime: number
    tokensUsed?: number
  }
}

// 预定义的润色样式
export const POLISH_STYLES: PolishStyle[] = [
  {
    value: 'formal',
    label: '正式商务',
    description: '适合商务场合，更专业的表达',
    icon: 'Medal'
  },
  {
    value: 'concise',
    label: '简洁清晰',
    description: '去除冗余，保持简明扼要',
    icon: 'Lightning'
  },
  {
    value: 'academic',
    label: '学术严谨',
    description: '适合学术论文和专业报告',
    icon: 'Document'
  },
  {
    value: 'friendly',
    label: '亲切自然',
    description: '口语化表达，更容易理解',
    icon: 'ChatDotRound'
  }
]

// AI 模型列表
export const AI_MODELS: AIModel[] = [
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    description: '快速响应，适合日常写作',
    category: 'basic',
    responseTime: '1-3秒',
    qualityRating: 3.5,
    bestFor: ['日常写作', '简单问题解答'],
    isActive: true,
    maxTokens: 4096,
    pricePerToken: 0.002
  },
  {
    id: 'gpt-4',
    name: 'GPT-4',
    description: '高质量输出，适合专业内容',
    category: 'advanced',
    responseTime: '3-5秒',
    qualityRating: 4.7,
    bestFor: ['专业内容创作', '复杂问题解答'],
    isActive: true,
    maxTokens: 8192,
    pricePerToken: 0.03
  }
]

export interface ModelPerformance {
  avgResponseTime: number
  successRate: number
  totalRequests: number
  lastUsed: Date
  errorCount: number
}