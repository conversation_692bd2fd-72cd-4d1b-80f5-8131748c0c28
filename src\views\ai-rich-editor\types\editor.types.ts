/**
 * 编辑器相关类型定义
 */

export interface EditorState {
  content: string
  selection: Selection | null
  history: HistoryItem[]
  isDirty: boolean
  wordCount: number
}

export interface HistoryItem {
  content: string
  timestamp: number
  action: string
}

export interface TextChange {
  start: number
  end: number
  oldText: string
  newText: string
}

export interface EditorToolbarProps {
  editor: any
  onCommand: (command: string, ...args: any[]) => void
}

export interface EditorSelection {
  text: string
  range: Range | null
  position: { x: number; y: number }
}