<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>AI富文本编辑器 - wangeditor风格+AI编辑+上传Word（按钮右上方）</title>\n  <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Inter:400,500,700&display=swap\">\n  <script src=\"https://cdn.tailwindcss.com\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/flowbite@2.0.0/dist/flowbite.min.js\"></script>\n  <script src=\"https://unpkg.com/lucide@latest/dist/umd/lucide.min.js\"></script>\n  <style>\n    body { font-family: Inter, var(--font-sans), sans-serif !important; background: #f6f7f9 !important; color: #222 !important; }\n    .editor-toolbar-wang { background: #fff; border-bottom: 1px solid #e5e7eb; padding: 0.5em 1em; display: flex; flex-wrap: wrap; gap: 0.5em; align-items: center; font-size: 15px; min-height: 48px; }\n    .editor-toolbar-wang .group { display: flex; align-items: center; gap: 0.25em; margin-right: 1.5em; }\n    .editor-toolbar-wang select, .editor-toolbar-wang button { background: transparent; border: none; color: #222; font-size: 15px; padding: 0.25em 0.5em; border-radius: 4px; cursor: pointer; transition: background 0.15s; outline: none; min-width: 32px; min-height: 32px; display: flex; align-items: center; gap: 2px; }\n    .editor-toolbar-wang button:hover, .editor-toolbar-wang select:hover { background: #f3f4f6; }\n    #uploadBtn { background: #2563eb !important; color: #fff !important; border: none !important; border-radius: 4px !important; font-weight: 500; box-shadow: 0 2px 8px 0 rgba(56,189,248,0.08); transition: background 0.15s, box-shadow 0.15s; min-width: 40px; min-height: 32px; padding: 0.25em 1em; margin-right: 1em; display: flex; align-items: center; gap: 4px; }\n    #uploadBtn:hover { background: #1d4ed8 !important; box-shadow: 0 4px 16px 0 rgba(56,189,248,0.16); }\n    .editor-wang { background: #fff; border: 1