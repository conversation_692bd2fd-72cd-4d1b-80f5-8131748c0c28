# 用户故事：智能文本润色功能实现

## 用户故事详情

**故事编号**: US-AI-001  
**故事标题**: 智能文本润色 - 一键优化选中文本  
**优先级**: P0 (最高优先级)  
**故事点**: 13  
**迭代**: Sprint 1  

### 用户故事
```
作为一个 行政助理
我想要 选中一段文字后一键润色
以便于 快速提高文档的专业性和可读性
```

### 场景示例
```
场景: 商务邮件润色
  假设 我正在撰写一封重要的客户邮件
  当 我选中邮件正文并点击"AI编辑"按钮
  而且 我选择"润色为更正式的商务语气"
  那么 AI应该返回一段更加专业、礼貌的文本
  而且 保留原有的核心信息和意图
```

## 技术实现要求

### 技术架构
- **前端框架**: Vue 3 + TypeScript + Composition API
- **UI组件库**: Element Plus 2.10.7
- **富文本编辑器**: WangEditor v5
- **AI服务**: api.agicto.cn/v1 (GPT-3.5-turbo/GPT-4-turbo)
- **状态管理**: Vue 3 Composition API (useEditorStore, useAIStore)

### 功能模块拆分

#### 1. 文本选择与UI组件
**组件**: `TextSelectionToolbar.vue`
**位置**: `src/views/ai-rich-editor/components/`

```typescript
// 组件接口定义
interface TextSelectionToolbarProps {
  selectedText: string
  position: { x: number; y: number }
  visible: boolean
}

interface TextSelectionToolbarEmits {
  'polish-text': (style: string) => void
  'close': () => void
}
```

**功能需求**:
- 实时监听文本选择事件
- 计算选择位置并显示悬浮工具栏
- 工具栏位置：选中文本上方精确居中
- 显示"AI润色"按钮
- 支持右键菜单集成

#### 2. AI交互模态框
**组件**: `AIPolishModal.vue`
**位置**: `src/views/ai-rich-editor/components/`

```typescript
// 模态框状态管理
interface AIPolishState {
  selectedText: string
  polishedText: string
  selectedStyle: string
  isProcessing: boolean
  prompt: string
}

// 润色风格选项
const POLISH_STYLES = [
  { value: 'formal', label: '正式商务', icon: 'Document' },
  { value: 'concise', label: '简洁明了', icon: 'Minus' },
  { value: 'friendly', label: '友好亲切', icon: 'ChatDotRound' },
  { value: 'professional', label: '专业严谨', icon: 'Medal' },
  { value: 'creative', label: '创意表达', icon: 'Lightning' }
] as const
```

#### 3. AI服务封装
**文件**: `src/utils/ai-provider.ts`

```typescript
// AI服务接口定义
interface AITextPolishRequest {
  text: string
  style: string
  model?: string
  maxTokens?: number
}

interface AITextPolishResponse {
  originalText: string
  polishedText: string
  changes: TextChange[]
  modelUsed: string
  processingTime: number
}

// 实现类
class AgictoAIProvider {
  async polishText(request: AITextPolishRequest): Promise<AITextPolishResponse> {
    // 实现API调用逻辑
  }
}
```

### 4. 编辑器集成
**组件**: `RichEditor.vue`
**位置**: `src/views/ai-rich-editor/components/`

#### 集成点：
- WangEditor v5自定义菜单项
- 选中文本事件监听
- 内容替换与历史记录
- 修改高亮显示

### 5. 状态管理
**文件**: `src/stores/editor.ts`

```typescript
interface EditorState {
  content: string
  selection: Selection | null
  history: HistoryItem[]
  isDirty: boolean
}

interface AIState {
  currentModel: string
  isProcessing: boolean
  lastResponse: string
  availableModels: AIModel[]
}
```

## 详细实现步骤

### Step 1: 项目结构创建 (2小时)
```
src/views/ai-rich-editor/
├── index.vue                 # 主页面组件
├── components/
│   ├── RichEditor.vue        # 富文本编辑器封装
│   ├── TextSelectionToolbar.vue  # 文本选择工具栏
│   ├── AIPolishModal.vue     # AI润色模态框
│   └── ModelSelector.vue     # 模型选择器
├── stores/
│   ├── editor.ts            # 编辑器状态管理
│   └── ai.ts               # AI相关状态管理
├── utils/
│   ├── ai-provider.ts       # AI服务封装
│   ├── text-processor.ts    # 文本处理工具
│   └── selection-utils.ts   # 选区操作工具
└── types/
    ├── ai.types.ts          # AI相关类型定义
    └── editor.types.ts      # 编辑器类型定义
```

### Step 2: 核心功能实现 (8小时)

#### 2.1 文本选择检测
```typescript
// src/utils/selection-utils.ts
export function useTextSelection() {
  const selectedText = ref('')
  const selectionRange = ref<Range | null>(null)
  const toolbarPosition = ref({ x: 0, y: 0 })

  const updateSelection = () => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      selectedText.value = selection.toString()
      selectionRange.value = selection.getRangeAt(0)
      toolbarPosition.value = calculateToolbarPosition(selection)
    } else {
      selectedText.value = ''
      selectionRange.value = null
    }
  }

  return {
    selectedText,
    selectionRange,
    toolbarPosition,
    updateSelection
  }
}
```

#### 2.2 AI服务集成
```typescript
// src/utils/ai-provider.ts
export class AgictoAIProvider {
  private baseURL = 'https://api.agicto.cn/v1'
  private API_KEY = import.meta.env.VITE_AGICTO_API_KEY

  async polishText(params: {
    text: string
    style: string
    model?: string
  }): Promise<string> {
    const prompt = this.buildPolishPrompt(params.text, params.style)
    
    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.API_KEY}`
      },
      body: JSON.stringify({
        model: params.model || 'gpt-3.5-turbo',
        messages: [{
          role: 'user',
          content: prompt
        }],
        max_tokens: 1000,
        temperature: 0.7
      })
    })

    const data = await response.json()
    return data.choices[0].message.content.trim()
  }

  private buildPolishPrompt(text: string, style: string): string {
    const stylePrompts = {
      formal: '请将以下文本润色为更正式的商务语气，使用专业礼貌的表达方式：',
      concise: '请将以下文本简化为简洁明了的表达，去除冗余词语：',
      friendly: '请将以下文本调整为友好亲切的语气，适合日常交流：',
      professional: '请将以下文本优化为专业严谨的表达方式，适合技术或学术场景：',
      creative: '请将以下文本改写为更有创意的表达方式，增加生动性：'
    }

    return `${stylePrompts[style]}\n\n原文：${text}\n\n要求：保留核心信息，仅优化表达方式，不添加新内容。`
  }
}
```

#### 2.3 模态框组件实现
```vue
<!-- AIPolishModal.vue -->
<template>
  <el-dialog
    v-model="visible"
    title="AI智能润色"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="polish-modal">
      <div class="original-text">
        <h4>原始文本</h4>
        <el-input
          v-model="originalText"
          type="textarea"
          :rows="3"
          readonly
        />
      </div>

      <div class="style-selector">
        <h4>润色风格</h4>
        <el-radio-group v-model="selectedStyle">
          <el-radio
            v-for="style in POLISH_STYLES"
            :key="style.value"
            :label="style.value"
          >
            <el-icon><component :is="style.icon" /></el-icon>
            {{ style.label }}
          </el-radio>
        </el-radio-group>
      </div>

      <div class="custom-prompt">
        <h4>自定义指令（可选）</h4>
        <el-input
          v-model="customPrompt"
          type="textarea"
          :rows="2"
          placeholder="例如：请用更专业的商务语言表达"
        />
      </div>

      <div v-if="isProcessing" class="processing">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>AI正在处理中...</span>
      </div>

      <div v-if="polishedText" class="result-preview">
        <h4>润色结果</h4>
        <el-input
          v-model="polishedText"
          type="textarea"
          :rows="4"
          readonly
        />
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="isProcessing"
          @click="handlePolish"
        >
          {{ isProcessing ? '处理中...' : '开始润色' }}
        </el-button>
        <el-button
          v-if="polishedText"
          type="success"
          @click="handleApply"
        >
          应用修改
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
```

### Step 3: 路由配置
```typescript
// src/router/index.ts
{
  path: '/ai-rich-editor',
  name: 'ai-rich-editor',
  component: () => import('@/views/ai-rich-editor/index.vue'),
  meta: {
    title: 'AI富文本编辑器',
    icon: 'Edit'
  }
}
```

## 验收标准

### 功能验收测试用例

#### 测试用例1: 文本选择与工具栏显示
- **前置条件**: 用户已进入AI富文本编辑器页面
- **操作步骤**:
  1. 在编辑器中输入一段文本
  2. 使用鼠标拖拽选中文本
  3. 观察工具栏出现位置和样式
- **预期结果**:
  - 工具栏出现在选中文本正上方居中位置
  - 工具栏包含"AI润色"按钮
  - 选中文本长度>3个字符时才显示工具栏

#### 测试用例2: 润色功能流程
- **前置条件**: 已选中文本，点击AI润色按钮
- **操作步骤**:
  1. 在模态框中选择"正式商务"风格
  2. 点击"开始润色"按钮
  3. 等待AI处理完成
  4. 查看润色结果
  5. 点击"应用修改"
- **预期结果**:
  - AI响应时间<2秒
  - 润色后的文本语气更正式专业
  - 原文被正确替换，保留核心信息
  - 支持撤销操作

#### 测试用例3: 错误处理
- **前置条件**: 网络连接不稳定或API服务异常
- **操作步骤**:
  1. 尝试进行文本润色
  2. 观察错误提示
- **预期结果**:
  - 显示友好的错误提示
  - 提供重试按钮
  - 保留原文本内容

### 性能要求
- **响应时间**: AI润色请求<2秒（95%百分位）
- **内存占用**: 编辑器初始化<50MB
- **兼容性**: 支持Chrome 90+, Firefox 88+, Edge 90+

### 用户体验要求
- **界面响应**: 所有交互响应<100ms
- **视觉反馈**: 操作状态有明确视觉提示
- **无障碍**: 支持键盘导航，屏幕阅读器兼容

## 开发任务分解

| 任务编号 | 任务描述 | 预估时间 | 依赖关系 |
|---------|----------|----------|----------|
| TASK-001 | 创建项目基础结构和路由配置 | 2小时 | 无 |
| TASK-002 | 集成WangEditor v5编辑器 | 3小时 | TASK-001 |
| TASK-003 | 实现文本选择检测工具 | 2小时 | TASK-002 |
| TASK-004 | 开发AI服务封装层 | 3小时 | 无 |
| TASK-005 | 实现文本选择工具栏组件 | 3小时 | TASK-003 |
| TASK-006 | 开发AI润色模态框组件 | 4小时 | TASK-004 |
| TASK-007 | 集成所有组件完成端到端测试 | 2小时 | TASK-005, TASK-006 |
| TASK-008 | 样式优化和响应式适配 | 2小时 | TASK-007 |
| TASK-009 | 错误处理和边界情况测试 | 2小时 | TASK-007 |

**总计开发时间**: 21小时

## 风险与注意事项

### 技术风险
1. **API限制**: api.agicto.cn/v1可能有调用频率限制，需要实现错误重试机制
2. **跨域问题**: 确保API支持CORS或配置代理
3. **大文本处理**: 长文本可能需要分批处理，需优化用户体验

### 用户体验风险
1. **工具栏遮挡**: 选中文本靠近边缘时工具栏可能显示不全
2. **移动端兼容**: 文本选择在移动设备上体验可能不佳
3. **AI结果不可预期**: 需要做好错误处理和用户引导

## 后续扩展点

### 功能扩展
1. **自定义润色风格**: 允许用户保存自定义prompt模板
2. **批量处理**: 支持整篇文档的批量润色
3. **版本对比**: 显示修改前后的对比视图
4. **快捷键支持**: 添加键盘快捷键快速润色

### 技术优化
1. **缓存机制**: 缓存常用润色结果
2. **流式响应**: 支持AI结果的流式展示
3. **离线模式**: 基础编辑功能离线可用

---

**创建日期**: 2025-08-23  
**创建者**: Bob (Scrum Master)  
**状态**: 审查 - 85%完成  
**关联文档**: 
- [AI富文本编辑器PRD文档](../prd/)
- [前端组件架构](../architecture/02-前端组件架构.md)

## QA结果

**评估日期**: 2025-08-24  
**评估者**: Quinn (Test Architect)  
**质量门禁**: CONCERNS  
**完成度**: 85%  

### ✅ 已实现功能
- **核心文本润色功能**: 完整实现，支持5种风格选择
- **富文本编辑器**: WangEditor v5集成完成
- **文本选择与工具栏**: 智能位置计算，用户体验良好
- **API集成**: api.agicto.cn/v1调用稳定，错误处理完善
- **用户体验优化**: 加载状态、撤销重做、快捷键支持

### ⚠️ 待完善功能
- **测试覆盖率**: 0% - 缺少单元测试和集成测试
- **Word文档导入**: 功能标记为TODO，影响行政助理核心工作流程
- **状态管理**: Pinia stores目录为空，使用简单响应式

### 🔴 门禁决策
**状态**: CONCERNS - 需要解决以下问题：
1. 添加核心功能单元测试（8小时工作量）
2. 完成Word文档导入功能（3小时工作量）

### 📋 建议优先级
1. **P0**: 添加测试套件（AI服务调用、组件交互、边界情况）
2. **P1**: 完成Word文档导入功能（使用已安装的mammoth依赖）
3. **P2**: 实现完整Pinia状态管理

**预计完成时间**: 2-3个工作日

**质量门禁文件**: `qa/gates/US-AI-001-smart-text-polish.yml`