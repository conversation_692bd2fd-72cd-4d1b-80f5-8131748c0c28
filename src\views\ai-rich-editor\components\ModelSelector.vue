<template>
  <div class="model-selector">
    <!-- 使用 v-if 确保组件完全加载后再渲染 -->
    <template v-if="currentModelConfig">
      <el-dropdown @command="handleModelChange" placement="bottom-end">
        <el-button type="primary" plain size="small">
          <el-icon><Cpu /></el-icon>
          <span class="model-name">{{ currentModelName }}</span>
          <el-icon class="el-icon--right"><ArrowDown /></el-icon>
        </el-button>
        
        <template #dropdown>
          <el-dropdown-menu class="model-dropdown-menu">
            <div class="model-dropdown-header">
              <span>选择AI模型</span>
              <el-button 
                type="text" 
                size="small" 
                @click="showModelDetails = true"
              >
                详情
              </el-button>
            </div>
            
            <el-dropdown-item 
              v-for="model in availableModels" 
              :key="model.id"
              :command="model.id"
              :disabled="!model.isActive"
              :class="{ 'model-active': model.id === currentModel }"
            >
              <div class="model-item">
                <div class="model-info">
                  <div class="model-name">{{ model.name }}</div>
                  <div class="model-description">{{ model.description }}</div>
                </div>
                <div class="model-meta">
                  <el-tag 
                    :type="getCategoryColor(model.category)" 
                    size="small"
                    class="model-category"
                  >
                    {{ getCategoryLabel(model.category) }}
                  </el-tag>
                  <div class="response-time">{{ model.responseTime }}</div>
                </div>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- 智能推荐按钮 -->
      <el-tooltip content="智能推荐模型" placement="bottom">
        <el-button 
          type="success" 
          plain 
          size="small"
          @click="showRecommendations"
          :loading="isRecommending"
          class="recommend-btn"
        >
          <el-icon><MagicStick /></el-icon>
        </el-button>
      </el-tooltip>
    </template>

    <!-- 模型详情面板 -->
    <el-drawer
      v-if="showModelDetails"
      v-model="showModelDetails"
      title="AI模型详情"
      direction="rtl"
      size="400px"
      :destroy-on-close="true"
    >
      <ModelDetailsPanel
        v-if="currentModelConfig"
        :current-model="currentModelConfig"
        :performance-metrics="{ [currentModel]: modelPerformance(currentModel) }"
      />
    </el-drawer>

    <!-- 推荐对话框 -->
    <el-dialog
      v-model="showRecommendation"
      title="模型推荐"
      width="500px"
      :destroy-on-close="true"
    >
      <ModelRecommendationPanel
        v-if="showRecommendation && recommendedModels.length"
        :recommended-models="recommendedModels"
        :current-context="recommendationContext"
        @select-model="handleRecommendedModel"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Cpu, ArrowDown, MagicStick } from '@element-plus/icons-vue'
import { useAIStore } from '../stores/ai'
import type { ModelRecommendationContext, ModelCategory } from '../types/ai.types'
import ModelDetailsPanel from './ModelDetailsPanel.vue'
import ModelRecommendationPanel from './ModelRecommendationPanel.vue'
import { ElMessage } from 'element-plus'

const aiStore = useAIStore()

// 状态
const showModelDetails = ref(false)
const showRecommendation = ref(false)
const isRecommending = ref(false)

// 计算属性
const currentModel = computed(() => aiStore.currentModel)
const currentModelName = computed(() => aiStore.currentModelConfig?.name || 'GPT-3.5 Turbo')
const availableModels = computed(() => aiStore.availableModels)
const currentModelConfig = computed(() => aiStore.currentModelConfig)

// 推荐上下文
const recommendationContext = ref<ModelRecommendationContext>({
  contentType: '',
  textLength: 0,
  urgency: 'medium',
  qualityRequirement: 'medium',
  language: '中文',
  useCase: ''
})

const recommendedModels = computed(() => {
  return aiStore.recommendedModels(recommendationContext.value)
})

const modelPerformance = computed(() => {
  return (modelId: string) => aiStore.modelPerformance(modelId)
})

// 方法
const handleModelChange = (modelId: string) => {
  aiStore.setCurrentModel(modelId)
}

const showRecommendations = async () => {
  isRecommending.value = true
  try {
    // 确保编辑器元素存在
    const editorContent = document.querySelector('.w-e-text')
    if (!editorContent) {
      throw new Error('编辑器未准备就绪')
    }
    
    const text = editorContent.textContent || ''
    
    // 更新推荐上下文
    recommendationContext.value = {
      contentType: analyzeContentType(text),
      textLength: text.length,
      urgency: analyzeUrgency(text),
      qualityRequirement: analyzeQualityRequirement(text),
      language: text.includes('中文') ? '中文' : '英文',
      useCase: determineUseCase(text)
    }
    
    showRecommendation.value = true
  } catch (error) {
    handleError(error as Error)
  } finally {
    isRecommending.value = false
  }
}

// 内容类型分析
function analyzeContentType(text: string): string {
  if (text.includes('项目') || text.includes('计划') || text.includes('方案')) {
    return '项目计划书'
  } else if (text.includes('邮件') || text.includes('回复') || text.includes('通知')) {
    return '商务邮件'
  } else if (text.includes('报告') || text.includes('总结') || text.includes('汇报')) {
    return '工作报告'
  } else if (text.includes('营销') || text.includes('推广') || text.includes('广告')) {
    return '营销文案'
  } else if (text.includes('技术') || text.includes('开发') || text.includes('API')) {
    return '技术文档'
  } else if (text.length < 100) {
    return '简短回复'
  } else {
    return '通用商务文档'
  }
}

// 紧急程度分析
function analyzeUrgency(text: string): 'high' | 'medium' | 'low' {
  const urgentKeywords = ['紧急', '尽快', '立即', '马上', '今天', '明天', '截止']
  const hasUrgentKeyword = urgentKeywords.some(keyword => text.includes(keyword))
  
  return hasUrgentKeyword ? 'high' : 'medium'
}

// 质量要求分析
function analyzeQualityRequirement(text: string): 'high' | 'medium' | 'low' {
  if (text.includes('重要') || text.includes('正式') || text.includes('关键') || text.length > 500) {
    return 'high'
  } else if (text.length < 50) {
    return 'low'
  } else {
    return 'medium'
  }
}

// 使用场景确定
function determineUseCase(text: string): string {
  if (text.includes('项目') || text.includes('计划')) return '项目规划'
  if (text.includes('邮件') || text.includes('回复')) return '邮件沟通'
  if (text.includes('报告') || text.includes('总结')) return '工作汇报'
  if (text.includes('营销') || text.includes('推广')) return '营销推广'
  if (text.includes('技术')) return '技术写作'
  return '商务写作'
}

const handleRecommendedModel = (modelId: string) => {
  aiStore.setCurrentModel(modelId)
  showRecommendation.value = false
}

const getCategoryLabel = (category: string) => {
  return aiStore.getModelCategoryLabel(category as ModelCategory)
}

const getCategoryColor = (category: string) => {
  return aiStore.getModelCategoryColor(category as ModelCategory)
}

// 添加初始化检查
const init = async () => {
  const store = useAIStore()
  await store.healthCheck()
}

// 在组件挂载后进行初始化
onMounted(() => {
  init()
})

// 添加错误处理
const handleError = (error: Error) => {
  ElMessage.error(error.message)
}
</script>

<style scoped>
.model-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.model-name {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.model-dropdown-menu {
  padding: 8px 0;
}

.model-dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  font-size: 14px;
  font-weight: 500;
}

.model-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  min-width: 300px;
}

.model-info {
  flex: 1;
  margin-right: 12px;
}

.model-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.model-description {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.model-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  min-width: 80px;
}

.model-category {
  margin-bottom: 4px;
}

.response-time {
  font-size: 11px;
  color: var(--el-text-color-secondary);
}

.model-active {
  background-color: var(--el-color-primary-light-9);
}

.recommend-btn {
  padding: 5px 8px;
}

:deep(.el-dropdown-menu__item) {
  line-height: 1.5;
  padding: 8px 16px;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: var(--el-fill-color-light);
}

.model-selector {
  width: 200px;
}

.model-option {
  display: flex;
  flex-direction: column;
}

.model-option small {
  color: #909399;
  font-size: 12px;
}
</style>