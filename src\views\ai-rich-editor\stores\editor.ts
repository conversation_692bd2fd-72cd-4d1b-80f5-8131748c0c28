import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface HistoryItem {
  id: string
  content: string
  timestamp: number
  action: string
  description?: string
}

export interface SelectionInfo {
  text: string
  range: Range | null
  startOffset: number
  endOffset: number
}

export const useEditorStore = defineStore('editor', () => {
  // 状态
  const content = ref('')
  const isDirty = ref(false)
  const history = ref<HistoryItem[]>([])
  const currentHistoryIndex = ref(-1)
  const selection = ref<SelectionInfo | null>(null)
  const isReadOnly = ref(false)

  // 计算属性
  const canUndo = computed(() => currentHistoryIndex.value > 0)
  const canRedo = computed(() => currentHistoryIndex.value < history.value.length - 1)
  const hasSelection = computed(() => selection.value?.text && selection.value.text.length > 0)
  const wordCount = computed(() => {
    const text = content.value.replace(/\u003c[^\u003e]*>/g, '')
    return text.trim() ? text.trim().split(/\s+/).length : 0
  })
  const characterCount = computed(() => {
    const text = content.value.replace(/\u003c[^\u003e]*>/g, '')
    return text.length
  })

  /**
   * 设置编辑器内容
   */
  const setContent = (newContent: string, action = 'set', description?: string) => {
    if (content.value !== newContent) {
      content.value = newContent
      isDirty.value = true
      addToHistory(newContent, action, description)
    }
  }

  /**
   * 获取编辑器内容
   */
  const getContent = () => content.value

  /**
   * 插入内容到光标位置
   */
  const insertContent = (newContent: string, description?: string) => {
    if (selection.value && selection.value.range) {
      const { range } = selection.value
      const selectionStart = range.startOffset
      const selectionEnd = range.endOffset
      
      const beforeSelection = content.value.substring(0, selectionStart)
      const afterSelection = content.value.substring(selectionEnd)
      
      const newFullContent = beforeSelection + newContent + afterSelection
      setContent(newFullContent, 'insert', description)
    } else {
      setContent(content.value + newContent, 'append', description)
    }
  }

  /**
   * 替换选中的内容
   */
  const replaceSelection = (newContent: string, description?: string) => {
    if (selection.value && selection.value.range) {
      const { range } = selection.value
      const selectionStart = range.startOffset
      const selectionEnd = range.endOffset
      
      const beforeSelection = content.value.substring(0, selectionStart)
      const afterSelection = content.value.substring(selectionEnd)
      
      const newFullContent = beforeSelection + newContent + afterSelection
      setContent(newFullContent, 'replace', description)
    }
  }

  /**
   * 设置选区信息
   */
  const setSelection = (selectionInfo: SelectionInfo | null) => {
    selection.value = selectionInfo
  }

  /**
   * 添加到历史记录
   */
  const addToHistory = (content: string, action: string, description?: string) => {
    const historyItem: HistoryItem = {
      id: `hist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      timestamp: Date.now(),
      action,
      description
    }

    // 如果当前不是最新状态，删除后面的历史记录
    if (currentHistoryIndex.value < history.value.length - 1) {
      history.value = history.value.slice(0, currentHistoryIndex.value + 1)
    }

    history.value.push(historyItem)
    currentHistoryIndex.value = history.value.length - 1

    // 限制历史记录数量（最多100条）
    if (history.value.length > 100) {
      history.value.shift()
      currentHistoryIndex.value--
    }
  }

  /**
   * 撤销操作
   */
  const undo = () => {
    if (canUndo.value) {
      currentHistoryIndex.value--
      const historyItem = history.value[currentHistoryIndex.value]
      content.value = historyItem.content
      isDirty.value = true
    }
  }

  /**
   * 重做操作
   */
  const redo = () => {
    if (canRedo.value) {
      currentHistoryIndex.value++
      const historyItem = history.value[currentHistoryIndex.value]
      content.value = historyItem.content
      isDirty.value = true
    }
  }

  /**
   * 保存当前状态
   */
  const save = () => {
    isDirty.value = false
  }

  /**
   * 重置编辑器
   */
  const reset = () => {
    content.value = ''
    isDirty.value = false
    history.value = []
    currentHistoryIndex.value = -1
    selection.value = null
  }

  /**
   * 从模板创建内容
   */
  const createFromTemplate = (templateName: string) => {
    const templates: Record<string, string> = {
      business: `<p>尊敬的 [收件人姓名]，</p>
<p><br></p>
<p>[正文内容]</p>
<p><br></p>
<p>此致<br>敬礼！</p>
<p>[您的姓名]<br>[日期]</p>`,
      
      memo: `<p><strong>备忘录</strong></p>
<p>日期：[日期]<br>主题：[主题]</p>
<p><br></p>
<p>[正文内容]</p>`,

      meeting: `<p><strong>会议纪要</strong></p>
<p>会议时间：[时间]<br>会议地点：[地点]<br>参会人员：[人员]</p>
<p><br></p>
<p><strong>会议内容：</strong></p>
<p>[内容]</p>
<p><br></p>
<p><strong>会议结论：</strong></p>
<p>[结论]</p>`
    }

    const templateContent = templates[templateName] || ''
    const processedContent = templateContent
      .replace(/\[日期\]/g, new Date().toLocaleDateString('zh-CN'))
      .replace(/\[时间\]/g, new Date().toLocaleString('zh-CN'))

    reset()
    setContent(processedContent, 'template', `从${templateName}模板创建`)
  }

  return {
    // 状态
    content,
    isDirty,
    history,
    currentHistoryIndex,
    selection,
    isReadOnly,

    // 计算属性
    canUndo,
    canRedo,
    hasSelection,
    wordCount,
    characterCount,

    // 方法
    setContent,
    getContent,
    insertContent,
    replaceSelection,
    setSelection,
    undo,
    redo,
    save,
    reset,
    createFromTemplate
  }
})