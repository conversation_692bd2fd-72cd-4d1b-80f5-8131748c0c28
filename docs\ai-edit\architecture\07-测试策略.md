# 测试策略

**文档版本**: v1.0  
**所属模块**: AI富文本编辑器架构  
**最后更新**: 2025-08-22

## 8. 测试策略

### 8.1 测试金字塔

```
          /\
         /  \
        / E2E \      10% 端到端测试
       /________\
      /    \    \
     /   集成  \   30% 集成测试
    /___________\
   /   |     |   \
  /    | 单元  \  60% 单元测试
 /_____|_____|____\
```

### 8.2 单元测试

#### 8.2.1 编辑器服务测试

```typescript
// __tests__/services/editor.service.test.ts
import { describe, it, expect, vi } from 'vitest'
import { EditorService } from '../../src/views/ai-rich-editor/services/editor.service'

describe('EditorService', () => {
  let service: EditorService
  
  beforeEach(() => {
    service = new EditorService()
  })
  
  describe('content manipulation', () => {
    it('should correctly count words', () => {
      const content = '<p>Hello world</p>'
      expect(service.getWordCount(content)).toBe(2)
    })
    
    it('should handle empty content', () => {
      expect(service.getWordCount('')).toBe(0)
    })
    
    it('should handle HTML tags', () => {
      const content = '<h1>Title</h1><p>This is a test</p>'
      expect(service.getWordCount(content)).toBe(4)
    })
  })
  
  describe('selection handling', () => {
    it('should extract selected text', () => {
      const mockSelection = {
        toString: () => 'selected text'
      }
      
      const result = service.getSelectedText(mockSelection as Selection)
      expect(result).toBe('selected text')
    })
  })
})
```

#### 8.2.2 AI服务测试

```typescript
// __tests__/services/ai.service.test.ts
import { describe, it, expect, vi } from 'vitest'
import { AIProvider } from '../../src/views/ai-rich-editor/services/ai.service'

describe('AIProvider', () => {
  let provider: AIProvider
  
  beforeEach(() => {
    provider = new AIProvider()
    vi.clearAllMocks()
  })
  
  describe('processText', () => {
    it('should process text successfully', async () => {
      const mockResponse = {
        data: {
          choices: [{ message: { content: 'optimized text' } }]
        }
      }
      
      vi.spyOn(provider.client, 'post').mockResolvedValue(mockResponse)
      
      const result = await provider.processText({
        text: 'test text',
        model: 'gpt-3.5-turbo',
        prompt: 'make it better'
      })
      
      expect(result).toBe('optimized text')
    })
    
    it('should handle API errors', async () => {
      const mockError = new Error('API Error')
      vi.spyOn(provider.client, 'post').mockRejectedValue(mockError)
      
      await expect(provider.processText({
        text: 'test',
        model: 'gpt-3.5-turbo',
        prompt: 'test'
      })).rejects.toThrow('AI处理失败: API Error')
    })
  })
})
```

### 8.3 集成测试

#### 8.3.1 文件上传流程测试

```typescript
// __tests__/integration/file-upload.test.ts
import { describe, it, expect, vi } from 'vitest'
import { FileUploadService } from '../../src/views/ai-rich-editor/services/file-upload.service'
import { WordParser } from '../../src/views/ai-rich-editor/services/word-parser.service'

describe('File Upload Integration', () => {
  let uploadService: FileUploadService
  let parser: WordParser
  
  beforeEach(() => {
    uploadService = new FileUploadService()
    parser = new WordParser()
  })
  
  it('should complete full upload and parse flow', async () => {
    const mockFile = new File(['mock content'], 'test.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })
    
    // 模拟上传进度
    const progressCallback = vi.fn()
    
    const result = await uploadService.uploadFile(mockFile, progressCallback)
    
    expect(result.status).toBe('completed')
    expect(result.file).toBe(mockFile)
    expect(progressCallback).toHaveBeenCalled()
  })
})
```

### 8.4 性能测试

#### 8.4.1 编辑器性能测试

```typescript
// __tests__/performance/editor-performance.test.ts
import { describe, it, expect } from 'vitest'
import { EditorService } from '../../src/views/ai-rich-editor/services/editor.service'

describe('Editor Performance', () => {
  let service: EditorService
  
  beforeEach(() => {
    service = new EditorService()
  })
  
  it('should handle large documents efficiently', () => {
    const largeContent = '<p>'.repeat(1000) + 'test'.repeat(1000) + '</p>'.repeat(1000)
    
    const startTime = performance.now()
    const wordCount = service.getWordCount(largeContent)
    const endTime = performance.now()
    
    expect(endTime - startTime).toBeLessThan(100) // 100ms以内
    expect(wordCount).toBeGreaterThan(0)
  })
  
  it('should maintain responsive UI during AI processing', async () => {
    const mockContent = 'test content'
    
    const startTime = performance.now()
    await service.processWithAI(mockContent, 'gpt-3.5-turbo', 'optimize')
    const endTime = performance.now()
    
    expect(endTime - startTime).toBeLessThan(3000) // 3秒以内
  })
})
```

### 8.5 端到端测试

#### 8.5.1 用户交互测试

```typescript
// __tests__/e2e/editor-flow.test.ts
import { test, expect } from '@playwright/test'

test.describe('AI富文本编辑器端到端测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/ai-rich-editor')
  })
  
  test('完整编辑流程', async ({ page }) => {
    // 1. 等待编辑器加载
    await page.waitForSelector('.rich-editor-container')
    
    // 2. 输入测试文本
    const editor = page.locator('.w-e-text-container')
    await editor.click()
    await editor.type('这是一段测试文本，用于验证AI编辑功能。')
    
    // 3. 选中文本
    await editor.selectText()
    
    // 4. 触发AI助手
    await page.click('button[title="AI助手"]')
    
    // 5. 输入AI指令
    const promptInput = page.locator('.ai-prompt-input')
    await promptInput.fill('请优化这段文本')
    
    // 6. 提交AI请求
    await page.click('.ai-submit-button')
    
    // 7. 验证结果
    await page.waitForSelector('.ai-response')
    const response = await page.textContent('.ai-response')
    expect(response).toBeTruthy()
    
    // 8. 应用AI结果
    await page.click('.ai-accept-button')
    
    // 9. 验证文本已更新
    const updatedContent = await editor.textContent()
    expect(updatedContent).not.toBe('这是一段测试文本，用于验证AI编辑功能。')
  })
  
  test('Word文件上传', async ({ page }) => {
    // 1. 准备测试文件
    const fileChooserPromise = page.waitForEvent('filechooser')
    
    // 2. 点击上传按钮
    await page.click('button[title="上传Word文档"]')
    
    // 3. 选择文件
    const fileChooser = await fileChooserPromise
    await fileChooser.setFiles('test-files/sample.docx')
    
    // 4. 等待上传完成
    await page.waitForSelector('.upload-success')
    
    // 5. 验证内容已加载
    const editorContent = await page.textContent('.w-e-text-container')
    expect(editorContent).toContain('Sample document content')
  })
})
```

### 8.6 测试配置

#### 8.6.1 Vitest配置

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/mockData/**'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  }
})
```

#### 8.6.2 Playwright配置

```typescript
// playwright.config.ts
import { defineConfig } from '@playwright/test'

export default defineConfig({
  testDir: './__tests__/e2e',
  timeout: 30 * 1000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    }
  ],
  webServer: {
    command: 'npm run dev',
    port: 5173,
    reuseExistingServer: !process.env.CI
  }
})
```

### 8.7 测试覆盖要求

| 测试类型 | 覆盖要求 | 工具选择 |
|----------|----------|----------|
| **单元测试** | ≥80%代码覆盖率 | Vitest |
| **集成测试** | 关键业务流程 | Vitest + MSW |
| **E2E测试** | 核心用户场景 | Playwright |
| **性能测试** | 关键路径性能 | Lighthouse |
| **安全测试** | 输入验证、XSS防护 | 自定义测试 |

### 8.8 持续集成测试

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x]
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit
      
      - name: Run integration tests
        run: npm run test:integration
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
      
      - name: Run E2E tests
        run: npm run test:e2e
        env:
          CI: true
```