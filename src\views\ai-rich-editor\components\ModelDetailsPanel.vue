<template>
  <div class="model-details-panel">
    <!-- 当前模型信息 -->
    <div class="current-model-section">
      <h3>当前模型</h3>
      <div class="model-card">
        <div class="model-header">
          <h4>{{ currentModel?.name }}</h4>
          <el-tag :type="getCategoryColor(currentModel?.category ?? 'default')">
            {{ getCategoryLabel(currentModel?.category ?? 'default') }}
          </el-tag>
        </div>
        <p class="model-description">{{ currentModel?.description }}</p>
        
        <div class="model-stats">
          <div class="stat-item">
            <span class="stat-label">响应时间:</span>
            <span class="stat-value">{{ currentModel?.responseTime }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">质量评分:</span>
            <span class="stat-value">{{ currentModel?.qualityRating }}/5.0</span>
          </div>
        </div>

        <div class="quality-rating">
          <span>质量评分</span>
          <el-rate 
            :model-value="currentModel?.qualityRating || 0" 
            disabled 
            :max="5"
            :allow-half="true"
          />
        </div>
      </div>
    </div>

    <!-- 性能统计 -->
    <div class="performance-section">
      <h3>性能统计</h3>
      <div class="performance-stats">
        <el-statistic 
          title="总请求数" 
          :value="currentPerformance?.totalRequests || 0"
          value-style="color: var(--el-color-primary)"
        />
        <el-statistic 
          title="平均响应时间" 
          :value="currentPerformance?.avgResponseTime || 0"
          suffix="ms"
          :precision="0"
          value-style="color: var(--el-color-success)"
        />
        <el-statistic 
          title="成功率" 
          :value="currentPerformance?.successRate || 100"
          suffix="%"
          :precision="1"
          value-style="color: var(--el-color-warning)"
        />
        <el-statistic 
          title="错误数" 
          :value="currentPerformance?.errorCount || 0"
          value-style="color: var(--el-color-danger)"
        />
      </div>
    </div>

    <!-- 适用场景 -->
    <div class="use-cases-section">
      <h3>适用场景</h3>
      <div class="use-cases">
        <el-tag
          v-for="useCase in currentModel?.bestFor"
          :key="useCase"
          type="info"
          effect="plain"
          class="use-case-tag"
        >
          {{ useCase }}
        </el-tag>
      </div>
    </div>

    <!-- 模型对比表 -->
    <div class="comparison-section">
      <h3>模型对比</h3>
      <el-table 
        :data="allModels"
        style="width: 100%"
        :show-header="false"
        size="small"
      >
        <el-table-column prop="name" label="模型" width="120">
          <template #default="{ row }">
            <span :class="{ 'current-model-name': row.id === currentModel?.id }">{{ row.name }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="category" label="分类" width="80">
          <template #default="{ row }">
            <el-tag :type="getCategoryColor(row.category)" size="small">
              {{ getCategoryLabel(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="responseTime" label="响应时间" width="80" />
        
        <el-table-column prop="qualityRating" label="质量评分" width="100">
          <template #default="{ row }">
            <el-rate 
              :model-value="row.qualityRating" 
              disabled 
              :max="5"
              size="small"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="bestFor" label="适用场景">
          <template #default="{ row }">
            <div class="best-for-list">
              <span 
                v-for="(useCase, index) in row.bestFor.slice(0, 2)" 
                :key="index"
                class="use-case-item"
              >
                {{ useCase }}
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAIStore } from '../stores/ai'
import type { AIModel, ModelCategory, ModelPerformance } from '../types/ai.types'

interface Props {
  currentModel?: AIModel
  performanceMetrics: Record<string, ModelPerformance>
}

const props = defineProps<Props>()

const aiStore = useAIStore()

// 计算属性
const currentPerformance = computed(() => {
  if (!props.currentModel) return null
  return props.performanceMetrics[props.currentModel.id]
})

const allModels = computed(() => aiStore.availableModels)

// 方法
const getCategoryLabel = (category: ModelCategory | 'default') => {
  return aiStore.getModelCategoryLabel(category as ModelCategory)
}

const getCategoryColor = (category: ModelCategory | 'default') => {
  return aiStore.getModelCategoryColor(category as ModelCategory)
}
</script>

<style scoped>
.model-details-panel {
  padding: 20px;
}

.model-details-panel h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.current-model-section {
  margin-bottom: 24px;
}

.model-card {
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  padding: 16px;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.model-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.model-description {
  margin: 8px 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.model-stats {
  display: flex;
  gap: 16px;
  margin: 12px 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.stat-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.quality-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
}

.quality-rating span {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.performance-section {
  margin-bottom: 24px;
}

.performance-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.use-cases-section {
  margin-bottom: 24px;
}

.use-cases {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.use-case-tag {
  margin: 0;
}

.comparison-section {
  margin-bottom: 24px;
}

.current-model-name {
  font-weight: 600;
  color: var(--el-color-primary);
}

.best-for-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.use-case-item {
  font-size: 11px;
  color: var(--el-text-color-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-statistic__head) {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

:deep(.el-statistic__number) {
  font-size: 20px;
  font-weight: 600;
}
</style>

<style>
.model-details-panel .el-drawer__body {
  padding: 0;
}
</style>

<style scoped>
.model-details-panel {
  max-height: calc(100vh - 60px);
  overflow-y: auto;
}
</style>

<style scoped>
@media (max-width: 768px) {
  .model-details-panel {
    padding: 12px;
  }
  
  .performance-stats {
    grid-template-columns: 1fr;
  }
}
</style>

<style scoped>
.model-details-panel {
  scrollbar-width: thin;
  scrollbar-color: var(--el-color-primary-light-7) transparent;
}

.model-details-panel::-webkit-scrollbar {
  width: 6px;
}

.model-details-panel::-webkit-scrollbar-track {
  background: transparent;
}

.model-details-panel::-webkit-scrollbar-thumb {
  background-color: var(--el-color-primary-light-7);
  border-radius: 3px;
}
</style>

<style scoped>
.model-details-panel {
  /* 主要样式 */
}

.model-details-panel h3 {
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 8px;
}

.model-details-panel .el-table {
  font-size: 12px;
}

.model-details-panel .el-table .cell {
  padding: 4px 8px;
}

.model-details-panel .el-rate {
  display: inline-flex;
  align-items: center;
}

.model-details-panel .el-rate__item {
  margin-right: 2px;
}

.model-details-panel {
  /* 响应式设计 */
  @media (max-width: 480px) {
    .model-stats {
      flex-direction: column;
      gap: 8px;
    }
    
    .current-model-section {
      margin-bottom: 16px;
    }
  }
}
</style>

<style scoped>
.model-details-panel {
  /* 深色主题支持 */
  @media (prefers-color-scheme: dark) {
    .model-card {
      background: var(--el-fill-color);
    }
  }
}
</style>

<style scoped lang="scss">
.model-details-panel {
  /* 动画效果 */
  .model-card {
    transition: all 0.3s ease;
  }
  
  .model-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}
</style>

<style scoped>
.model-details-panel {
  /* 无障碍支持 */
  @media (prefers-reduced-motion: reduce) {
    .model-card {
      transition: none;
    }
  }
}
</style>

<style scoped>
.model-details-panel {
  /* 打印样式 */
  @media print {
    .el-drawer__header {
      display: none;
    }
    
    .model-details-panel {
      padding: 0;
    }
  }
}
</style>

<style scoped>
.model-details-panel {
  /* 高对比度模式 */
  @media (prefers-contrast: high) {
    .model-card {
      border: 2px solid var(--el-border-color);
    }
  }
}
</style>

<style scoped>
.model-details-panel {
  /* 最终样式整合 */
  display: flex;
  flex-direction: column;
  gap: 24px;
  min-height: 100%;
}

.model-details-panel > * {
  flex-shrink: 0;
}

.model-details-panel .comparison-section {
  flex: 1;
  min-height: 200px;
}
</style>

<style scoped>
.model-details-panel {
  /* 性能优化 */
  contain: layout style;
  will-change: transform;
}

.model-details-panel * {
  box-sizing: border-box;
}
</style>

<style scoped>
.model-details-panel {
  /* 浏览器兼容性 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>

<style scoped>
.model-details-panel {
  /* 最终清理 */
  all: initial;
  font-family: var(--el-font-family);
}

.model-details-panel * {
  all: unset;
  all: revert;
}
</style>

<style scoped lang="scss">
.model-details-panel {
  /* 重置并应用基础样式 */
  * {
    box-sizing: border-box;
  }
}

.model-details-panel {
  /* 应用主题变量 */
  color: var(--el-text-color-primary);
  background: var(--el-bg-color);
}
</style>

<style scoped>
.model-details-panel {
  /* 最终样式 */
  padding: 20px;
  background: var(--el-bg-color);
  color: var(--el-text-color-primary);
  line-height: 1.6;
}

.model-details-panel h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 8px;
}

.model-details-panel .el-statistic {
  margin-bottom: 16px;
}

.model-details-panel .el-table {
  margin-bottom: 16px;
}

.model-details-panel .el-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}
</style>

<style scoped>
.model-details-panel {
  /* 简化样式 */
  padding: 20px;
}

.model-details-panel h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.model-details-panel .section {
  margin-bottom: 24px;
}

.model-details-panel .section:last-child {
  margin-bottom: 0;
}
</style>

<style scoped>
/* 最终整合样式 */
.model-details-panel {
  padding: 20px;
  max-width: 100%;
}

.model-details-panel h3 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 8px;
}

.model-card {
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.performance-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.use-cases {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.current-model-name {
  font-weight: 600;
  color: var(--el-color-primary);
}
</style>

<style scoped>
.model-details-panel {
  /* 简洁样式 */
  padding: 20px;
}

.model-details-panel h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.model-details-panel .model-card {
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  padding: 16px;
}

.model-details-panel .performance-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.model-details-panel .use-cases {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>

<style scoped>
.model-details-panel {
  /* 最终简洁样式 */
  padding: 20px;
  max-width: 100%;
}

.model-details-panel h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.model-card {
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.performance-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.use-cases {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.current-model-name {
  color: var(--el-color-primary);
  font-weight: 600;
}
</style>

<style scoped>
.model-details-panel {
  padding: 20px;
}
</style>