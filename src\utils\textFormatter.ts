/**
 * 文本格式化工具
 * 用于格式化清理后的文本
 */

export interface FormatOptions {
  indentFirstLine?: boolean
  indentSize?: number
  paragraphSpacing?: number
  removeEmptyLines?: boolean
}

const defaultOptions: FormatOptions = {
  indentFirstLine: false,
  indentSize: 2,
  paragraphSpacing: 1,
  removeEmptyLines: false
}

/**
 * 清理行首多余空格（在无缩进模式下使用）
 */
function cleanLineLeadingSpaces(text: string): string {
  // 分割每一行，移除行首空格，但保持原有的空行结构
  const lines = text.split('\n')
  
  return lines
    .map(line => line.replace(/^\s+/, '')) // 只移除行首空格，保持空行
    .join('\n')
}

/**
 * 应用缩进（针对每个非空行）
 */
function applyIndent(text: string, indentSize: number): string {
  const indent = ' '.repeat(indentSize)
  
  // 分割每一行，保持原有的空行结构
  const lines = text.split('\n')
  
  return lines
    .map(line => {
      const trimmedLine = line.replace(/^\s+/, '') // 清理行首空格
      
      if (trimmedLine.trim()) {
        // 非空行，添加缩进
        return indent + trimmedLine
      } else {
        // 空行，保持为空
        return ''
      }
    })
    .join('\n')
}


/**
 * 移除空行
 */
function removeEmptyLines(text: string): string {
  return text
    .split('\n')
    .filter(line => line.trim().length > 0)
    .join('\n')
}

/**
 * 主要格式化函数
 */
export function formatText(text: string, options: FormatOptions = {}): string {
  const opts = { ...defaultOptions, ...options }
  
  
  let formatted = text
  
  if (opts.removeEmptyLines) {
    formatted = removeEmptyLines(formatted)
  }
  

  
  if (opts.indentFirstLine) {
    formatted = applyIndent(formatted, opts.indentSize || 2)
  } else {
    // 无缩进模式下，清理行首多余空格
    formatted = cleanLineLeadingSpaces(formatted)
  }
  
  // formatted = normalizePunctuation(formatted)
  
  return formatted
}

/**
 * 获取文本统计信息
 */
export function getTextStats(text: string) {
  const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0)
  const lines = text.split('\n')
  const words = text.trim().split(/\s+/).filter(w => w.length > 0)
  const chars = text.replace(/\s/g, '').length
  
  return {
    paragraphs: paragraphs.length,
    lines: lines.length,
    words: words.length,
    characters: chars
  }
}