import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { AIModel, ModelPerformance, ModelRecommendationContext, ModelCategory, PolishResponse } from '../types/ai.types'
import { AgictoAIProvider } from '../utils/ai-provider'
import { AI_MODELS } from '../types/ai.types'

// 定义缺失的类型
interface AITextPolishResponse extends PolishResponse {
  originalText: string
  processingTime: number
  tokensUsed?: number
}

interface TextStyle {
  value: string
  label: string
  icon: string
  description: string
}


export const useAIStore = defineStore('ai', () => {
  // 状态
  const currentModel = ref('gpt-3.5-turbo')
  const isProcessing = ref(false)
  const lastResponse = ref<AITextPolishResponse | null>(null)
  const error = ref<string | null>(null)
  const requestHistory = ref<AITextPolishResponse[]>([])
  const apiKey = ref('')
  const modelPreferences = ref<Record<string, string>>({})
  const lastUsedModels = ref<string[]>([])
  const performanceMetrics = ref<Record<string, ModelPerformance>>({})
  const recommendedModel = ref<string>('')

  // AI服务提供商
  const aiProvider = new AgictoAIProvider()

  // 可用模型列表
  const availableModels = ref<AIModel[]>(AI_MODELS)

  // 润色风格选项
  const polishStyles = ref<TextStyle[]>([
    {
      value: 'formal',
      label: '正式商务',
      icon: 'Document',
      description: '使用专业礼貌的商务语言表达'
    },
    {
      value: 'concise',
      label: '简洁明了',
      icon: 'Minus',
      description: '去除冗余词语，简洁表达'
    },
    {
      value: 'friendly',
      label: '友好亲切',
      icon: 'ChatDotRound',
      description: '适合日常交流的友好语气'
    },
    {
      value: 'professional',
      label: '专业严谨',
      icon: 'Medal',
      description: '适合技术或学术场景的专业表达'
    },
    {
      value: 'creative',
      label: '创意表达',
      icon: 'Lightning',
      description: '更有创意的生动表达方式'
    }
  ])

  // 计算属性
  const canProcess = computed(() => !isProcessing.value && apiKey.value)
  const hasError = computed(() => error.value !== null)
  const requestCount = computed(() => requestHistory.value.length)
  const totalTokensUsed = computed(() => 
    requestHistory.value.reduce((total, response) => total + (response.tokensUsed || 0), 0)
  )
  const currentModelConfig = computed(() => 
    availableModels.value.find(model => model.id === currentModel.value)
  )
  
  // 推荐模型计算属性
  const recommendedModels = computed(() => (context: ModelRecommendationContext) => {
    return availableModels.value.filter(model => {
      // 基于内容类型推荐
      if (context.contentType.includes('中文') && model.category === 'chinese') {
        return true
      }
      // 基于紧急程度推荐
      if (context.urgency === 'high' && model.category === 'speed') {
        return true
      }
      // 基于质量要求推荐
      if (context.qualityRequirement === 'high' && model.category === 'quality') {
        return true
      }
      return false
    })
  })

  // 模型性能统计
  const modelPerformance = computed(() => (modelId: string) => {
    return performanceMetrics.value[modelId] || {
      avgResponseTime: 0,
      successRate: 100,
      totalRequests: 0,
      lastUsed: new Date(),
      errorCount: 0
    }
  })

  /**
   * 文本润色
   */
  const polishText = async (text: string, style: string, customPrompt?: string) => {
    if (!canProcess.value) {
      throw new Error('无法处理文本：AI服务不可用')
    }

    isProcessing.value = true
    error.value = null

    try {
      const response = await aiProvider.polishText({
        text,
        style,
        model: currentModel.value,
        customPrompt,
        maxTokens: 1000
      })

      lastResponse.value = response
      requestHistory.value.unshift(response)

      // 限制历史记录数量
      if (requestHistory.value.length > 50) {
        requestHistory.value = requestHistory.value.slice(0, 50)
      }

      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '未知错误'
      throw err
    } finally {
      isProcessing.value = false
    }
  }

  /**
   * 健康检查
   */
  const healthCheck = async (): Promise<boolean> => {
    try {
      const isHealthy = await aiProvider.healthCheck()
      if (!isHealthy) {
        error.value = 'AI服务连接失败'
      }
      return isHealthy
    } catch (err) {
      error.value = err instanceof Error ? err.message : '健康检查失败'
      return false
    }
  }

  /**
   * 获取模型信息
   */
  const getModelInfo = (modelId: string) => {
    return availableModels.value.find(model => model.id === modelId)
  }

  /**
   * 清除错误
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * 清除历史记录
   */
  const clearHistory = () => {
    requestHistory.value = []
    lastResponse.value = null
  }

  /**
   * 设置API密钥
   */
  const setApiKey = (key: string) => {
    apiKey.value = key
    // 更新环境变量
    import.meta.env.VITE_AGICTO_API_KEY = key
  }

  /**
   * 获取使用统计
   */
  const getUsageStats = () => {
    return {
      totalRequests: requestCount.value,
      totalTokens: totalTokensUsed.value,
      averageProcessingTime: requestHistory.value.length > 0 
        ? requestHistory.value.reduce((sum, res) => sum + res.processingTime, 0) / requestHistory.value.length 
        : 0,
      successRate: requestHistory.value.length > 0 
        ? (requestHistory.value.filter(res => res.polishedText !== res.originalText).length / requestHistory.value.length) * 100 
        : 0
    }
  }

  /**
   * 设置当前模型
   */
  const setCurrentModel = (modelId: string) => {
    if (availableModels.value.find(model => model.id === modelId && model.isActive)) {
      currentModel.value = modelId
      updateLastUsed(modelId)
    }
  }

  /**
   * 更新最后使用模型
   */
  const updateLastUsed = (modelId: string) => {
    lastUsedModels.value = [modelId, ...lastUsedModels.value.filter(id => id !== modelId)].slice(0, 5)
  }

  /**
   * 更新模型性能指标
   */
  const updateModelMetrics = (modelId: string, metrics: { responseTime: number; success: boolean }) => {
    if (!performanceMetrics.value[modelId]) {
      performanceMetrics.value[modelId] = {
        avgResponseTime: metrics.responseTime,
        successRate: metrics.success ? 100 : 0,
        totalRequests: 1,
        lastUsed: new Date(),
        errorCount: metrics.success ? 0 : 1
      }
    } else {
      const existing = performanceMetrics.value[modelId]
      const totalRequests = existing.totalRequests + 1
      existing.avgResponseTime = ((existing.avgResponseTime * existing.totalRequests) + metrics.responseTime) / totalRequests
      existing.successRate = ((existing.successRate * existing.totalRequests) + (metrics.success ? 100 : 0)) / totalRequests
      existing.totalRequests = totalRequests
      existing.lastUsed = new Date()
      if (!metrics.success) {
        existing.errorCount++
      }
    }
  }

  /**
   * 获取推荐模型
   */
  const getRecommendedModel = (context: ModelRecommendationContext): string => {
    const { contentType, textLength, urgency, qualityRequirement } = context

    // 基于内容类型推荐
    if (contentType.includes('中文') || contentType.includes('中文')) {
      return 'kimi-k2-0711-preview'
    }

    // 基于文本长度和紧急程度
    if (textLength < 100 && urgency === 'high') {
      return 'gpt-3.5-turbo'
    }

    // 基于质量要求
    if (qualityRequirement === 'high') {
      return 'gpt-4-turbo'
    }

    return 'gpt-3.5-turbo' // 默认模型
  }

  /**
   * 获取模型分类标签
   */
  const getModelCategoryLabel = (category: ModelCategory) => {
    const labels: Record<ModelCategory, string> = {
      basic: '基础版',
      advanced: '高级版',
      specialized: '专业版',
      speed: '速度优先',
      quality: '质量优先',
      chinese: '中文优化',
      multilingual: '多语言'
    }
    return labels[category] || '未知'
  }

  /**
   * 获取模型分类颜色
   */
  const getModelCategoryColor = (category: ModelCategory) => {
    const colors: Record<ModelCategory, string> = {
      basic: 'info',
      advanced: 'success',
      specialized: 'warning',
      speed: 'primary',
      quality: 'success',
      chinese: 'danger',
      multilingual: 'warning'
    }
    return colors[category] || 'info'
  }

  return {
    // 状态
    currentModel,
    isProcessing,
    lastResponse,
    error,
    requestHistory,
    apiKey,
    availableModels,
    polishStyles,
    modelPreferences,
    lastUsedModels,
    performanceMetrics,
    recommendedModel,

    // 计算属性
    canProcess,
    hasError,
    requestCount,
    totalTokensUsed,
    currentModelConfig,
    recommendedModels,
    modelPerformance,

    // 方法
    polishText,
    healthCheck,
    getModelInfo,
    clearError,
    clearHistory,
    setApiKey,
    getUsageStats,
    setCurrentModel,
    updateModelMetrics,
    getRecommendedModel,
    getModelCategoryLabel,
    getModelCategoryColor
  }
})