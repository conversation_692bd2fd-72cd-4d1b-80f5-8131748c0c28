<template>
  <div class="word-import">
    <input
      type="file"
      ref="fileInput"
      accept=".docx,.doc"
      style="display: none"
      @change="handleFileInputChange"
    >
    <el-button @click="triggerFileInput">
      <el-icon><Upload /></el-icon>
      导入Word
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Upload } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import mammoth from 'mammoth'

const emit = defineEmits<{
  (e: 'import-success', content: { html: string; text: string }, file: File): void
  (e: 'import-error', error: Error): void
}>()

const fileInput = ref<HTMLInputElement | null>(null)

const triggerFileInput = () => {
  fileInput.value?.click()
}

// 简化的后处理HTML内容，避免复杂的DOM操作
const postProcessHtml = (html: string): string => {
  console.log('原始HTML:', html)

  let processedHtml = html

  // 处理无序列表项 - 使用多个简单的替换避免复杂正则表达式
  // 处理常见的项目符号
  processedHtml = processedHtml.replace(/<p>([•·▪▫◦‣⁃○■□▲△●◆◇]\s*)(.*?)<\/p>/g, '<li>$2</li>')

  // 处理文本符号（分别处理避免字符类问题）
  processedHtml = processedHtml.replace(/<p>([-]\s*)(.*?)<\/p>/g, '<li>$2</li>')
  processedHtml = processedHtml.replace(/<p>([\*]\s*)(.*?)<\/p>/g, '<li>$2</li>')
  processedHtml = processedHtml.replace(/<p>([\+]\s*)(.*?)<\/p>/g, '<li>$2</li>')

  // 处理有序列表项（数字和字母）
  processedHtml = processedHtml.replace(
    /<p>(\d+[.)]\s*)(.*?)<\/p>/g,
    '<li>$2</li>'
  )

  processedHtml = processedHtml.replace(
    /<p>([a-zA-Z][.)]\s*)(.*?)<\/p>/g,
    '<li>$2</li>'
  )

  // 将连续的li标签包装在ul标签中
  processedHtml = processedHtml.replace(
    /(<li>.*?<\/li>)(\s*<li>.*?<\/li>)*/gs,
    '<ul>$&</ul>'
  )

  // 合并相邻的ul标签
  processedHtml = processedHtml.replace(/<\/ul>\s*<ul>/g, '')

  // 检测可能的标题
  processedHtml = processedHtml.replace(
    /^<p>(.*?申请.*?|.*?报告.*?|.*?总结.*?|.*?计划.*?|.*?书.*?)<\/p>/,
    '<h1>$1</h1>'
  )

  console.log('处理后HTML:', processedHtml)
  return processedHtml
}

const handleFileInputChange = async (event: Event) => {
  const input = event.target as HTMLInputElement
  const file = input.files?.[0]

  if (!file) return

  try {
    ElMessage.info('正在解析文档...')

    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        if (!e.target?.result) {
          throw new Error('文件读取失败')
        }

        const arrayBuffer = e.target.result as ArrayBuffer
        const result = await mammoth.convertToHtml(
          { arrayBuffer },
          {
            styleMap: [
              // 标题样式映射
              "p[style-name='标题'] => h1:fresh",
              "p[style-name='Title'] => h1:fresh",
              "p[style-name='Heading 1'] => h1:fresh",
              "p[style-name='标题 1'] => h2:fresh",
              "p[style-name='Heading 2'] => h2:fresh",

              // 列表样式映射 - 直接映射为标准的ul/li结构
              "p[style-name='列表段落'] => li:fresh",
              "p[style-name='List Paragraph'] => li:fresh",
              "p[style-name='ListParagraph'] => li:fresh",
              "p[style-name='Bullet'] => li:fresh",
              "p[style-name='项目符号'] => li:fresh",

              // 正文段落
              "p[style-name='正文'] => p:fresh",
              "p[style-name='Normal'] => p:fresh",
              "p[style-name='Body Text'] => p:fresh",
              "p => p:fresh",
            ],
            transformDocument: (element: any) => {
              // 检测可能的标题：如果段落文本较短且可能是标题
              if (element.type === 'paragraph') {
                const text = element.children?.map((child: any) =>
                  child.type === 'text' ? child.value : ''
                ).join('').trim() || '';

                // 如果文本是"转正申请书"或类似的短标题，强制设为主标题
                if (text.length < 20 && (
                  text.includes('申请') ||
                  text.includes('报告') ||
                  text.includes('总结') ||
                  text.match(/^[^\u4e00-\u9fa5]*[\u4e00-\u9fa5]{2,10}[^\u4e00-\u9fa5]*$/)
                )) {
                  return {
                    ...element,
                    styleName: '标题'
                  };
                }
              }
              return element;
            }
          }
        )

        // 添加调试信息
        console.log('Mammoth解析结果:', result.value)
        console.log('Mammoth警告信息:', result.messages)

        // 后处理HTML内容，自动检测和格式化标题
        const processedHtml = postProcessHtml(result.value)

        emit('import-success', {
          html: processedHtml,
          text: result.value.replace(/<[^>]*>/g, '')
        }, file)

        ElMessage.success('文档导入成功')
      } catch (error) {
        handleError(error)
      }
    }

    reader.onerror = () => {
      handleError(new Error('文件读取失败'))
    }

    reader.readAsArrayBuffer(file)
  } catch (error) {
    handleError(error)
  } finally {
    // 清除文件输入，允许重复选择同一文件
    input.value = ''
  }
}

const handleError = (error: unknown) => {
  console.error('Word导入错误:', error)
  emit('import-error', error instanceof Error ? error : new Error('导入失败'))
  ElMessage.error(`导入失败: ${error instanceof Error ? error.message : '未知错误'}`)
}
</script>

<style scoped>
.word-import {
  display: inline-block;
}
</style>