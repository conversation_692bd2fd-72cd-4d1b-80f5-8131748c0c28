# API集成方案

**文档版本**: v1.0  
**所属模块**: AI富文本编辑器架构  
**最后更新**: 2025-08-22

## 4. API集成方案

### 4.1 API客户端架构

#### 4.1.1 AI服务封装 (ai.service.ts)

```typescript
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'

interface AIRequest {
  model: string
  messages: Array<{
    role: 'system' | 'user' | 'assistant'
    content: string
  }>
  max_tokens?: number
  temperature?: number
  stream?: boolean
}

interface AIResponse {
  id: string
  choices: Array<{
    message: {
      content: string
    }
    finish_reason: string
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

export class AIProvider {
  private client: AxiosInstance
  private baseURL = 'https://api.agicto.cn/v1'
  
  constructor() {
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${import.meta.env.VITE_AI_API_KEY}`
      }
    })
    
    this.setupInterceptors()
  }
  
  private setupInterceptors() {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 添加请求时间戳
        config.metadata = { startTime: Date.now() }
        return config
      },
      (error) => Promise.reject(error)
    )
    
    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        // 记录响应时间
        const duration = Date.now() - response.config.metadata.startTime
        console.log(`API响应时间: ${duration}ms`)
        return response
      },
      (error) => {
        // 统一错误处理
        this.handleError(error)
        return Promise.reject(error)
      }
    )
  }
  
  async processText(params: {
    text: string
    model: string
    prompt: string
    maxTokens?: number
    temperature?: number
  }): Promise<string> {
    const request: AIRequest = {
      model: params.model,
      messages: [
        {
          role: 'system',
          content: '你是一个专业的文档编辑助手，请根据用户要求优化文本内容，保持专业性和准确性。'
        },
        {
          role: 'user',
          content: `请优化以下文本：\n\n${params.text}\n\n要求：${params.prompt}`
        }
      ],
      max_tokens: params.maxTokens || 1000,
      temperature: params.temperature || 0.7
    }
    
    try {
      const response = await this.client.post<AIResponse>('/chat/completions', request)
      return response.data.choices[0]?.message.content || ''
    } catch (error) {
      throw new Error(`AI处理失败: ${error.message}`)
    }
  }
  
  async getModels(): Promise<AIModel[]> {
    try {
      const response = await this.client.get('/models')
      return response.data.data.map((model: any) => ({
        id: model.id,
        name: model.id,
        description: model.description || '',
        maxTokens: model.max_tokens
      }))
    } catch (error) {
      // 返回默认模型列表
      return [
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: '快速响应，适合日常写作', maxTokens: 4096 },
        { id: 'gpt-4', name: 'GPT-4', description: '高质量内容，适合专业文档', maxTokens: 8192 },
        { id: 'kimi-k2-0711-preview', name: 'Kimi K2', description: '中文优化，本土化写作', maxTokens: 128000 }
      ]
    }
  }
  
  private handleError(error: any) {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          console.error('API认证失败')
          break
        case 429:
          console.error('请求频率过高')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error(`API错误: ${error.response.status}`)
      }
    } else if (error.request) {
      console.error('网络连接失败')
    } else {
      console.error('请求配置错误')
    }
  }
}
```

### 4.2 错误处理与重试机制

```typescript
export class RetryableAIProvider extends AIProvider {
  private maxRetries = 3
  private retryDelay = 1000 // 初始延迟1秒
  
  async processTextWithRetry(params: any): Promise<string> {
    let lastError: Error
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await this.processText(params)
      } catch (error) {
        lastError = error as Error
        
        if (attempt === this.maxRetries) {
          throw lastError
        }
        
        // 指数退避
        const delay = this.retryDelay * Math.pow(2, attempt - 1)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    throw lastError!
  }
}
```

### 4.3 API使用规范

#### 4.3.1 请求限制

| 限制类型 | 值 | 说明 |
|----------|----|------|
| **单次请求最大token数** | 4000 | GPT-3.5限制 |
| **每分钟最大请求数** | 60 | 避免频率限制 |
| **单次请求超时时间** | 10秒 | 用户体验优化 |
| **重试最大次数** | 3次 | 防止无限重试 |

#### 4.3.2 错误码映射

| HTTP状态码 | 业务含义 | 处理策略 |
|------------|----------|----------|
| **200** | 成功 | 正常处理 |
| **401** | 认证失败 | 提示用户检查API密钥 |
| **429** | 频率限制 | 延迟重试，提示用户 |
| **500** | 服务器错误 | 指数退避重试 |
| **503** | 服务不可用 | 稍后重试 |

#### 4.3.3 请求优化

```typescript
// 请求缓存
export class APIRateLimiter {
  private requests: Map<string, number[]> = new Map()
  private maxRequestsPerMinute = 60
  
  async throttle(key: string, fn: () => Promise<any>): Promise<any> {
    const now = Date.now()
    const requests = this.requests.get(key) || []
    
    // 清理1分钟前的请求记录
    const validRequests = requests.filter(time => now - time < 60000)
    
    if (validRequests.length >= this.maxRequestsPerMinute) {
      const waitTime = 60000 - (now - validRequests[0])
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }
    
    validRequests.push(now)
    this.requests.set(key, validRequests)
    
    return fn()
  }
}
```

### 4.4 安全性考虑

#### 4.4.1 API密钥管理

```typescript
// 环境变量配置
export const APIConfig = {
  baseURL: import.meta.env.VITE_AI_API_BASE_URL || 'https://api.agicto.cn/v1',
  apiKey: import.meta.env.VITE_AI_API_KEY,
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
  maxRetries: parseInt(import.meta.env.VITE_API_MAX_RETRIES || '3')
}

// 运行时验证
export function validateAPIConfig(): boolean {
  if (!APIConfig.apiKey) {
    console.error('API密钥未配置')
    return false
  }
  
  if (!APIConfig.baseURL) {
    console.error('API基础URL未配置')
    return false
  }
  
  return true
}
```

#### 4.4.2 请求数据脱敏

```typescript
export class SensitiveDataFilter {
  private patterns = [
    /\b\d{15}\d*[xX]?\b/g, // 身份证号
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // 邮箱
    /\b1[3-9]\d{9}\b/g // 手机号
  ]
  
  filter(text: string): string {
    let filtered = text
    
    this.patterns.forEach(pattern => {
      filtered = filtered.replace(pattern, '[敏感信息]')
    })
    
    return filtered
  }
}
```