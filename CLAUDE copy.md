# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概览

这是一个基于 Vue 3 + TypeScript + Vite 的企业内部工具集应用，为公司内部员工提供各种实用工具。项目采用模块化设计，支持多种工具类型的扩展。

## 项目目标

- **主要用途**: 企业内部工具集，提升员工工作效率
- **当前功能**: 文本格式化、数据合并Excel生成
- **未来规划**: 文档查询工具、数据爬虫工具（Puppeteer）

## 常用命令

- **开发服务器**: `npm run dev` - 启动本地开发服务器
- **构建项目**: `npm run build` - 构建生产版本（先运行类型检查再构建）
- **预览构建**: `npm run preview` - 预览构建结果

## 技术栈

### 核心技术
- **框架**: Vue 3 (Composition API + `<script setup>`)
- **语言**: TypeScript (严格模式)
- **构建工具**: Vite
- **UI框架**: Element Plus
- **路由**: Vue Router 4
- **依赖管理**: npm

### 业务库
- **Excel处理**: xlsx
- **图标库**: @element-plus/icons-vue

### 未来技术栈
- **浏览器自动化**: Puppeteer (用于数据爬虫)
- **文档查询**: 待定技术方案

## 项目结构

```
src/
├── main.ts              # 应用入口文件
├── App.vue              # 根组件
├── router/              # 路由配置
│   └── index.ts         # 路由定义
├── components/          # 公共组件
│   └── AppLayout.vue    # 应用布局组件
├── views/               # 页面组件（按工具分类）
│   ├── text-formatter/  # 文本格式化工具
│   │   └── index.vue
│   └── data-merge-excel/ # 数据合并Excel工具
│       └── index.vue
├── utils/               # 工具函数（按功能分类）
│   ├── clipboard.ts     # 剪贴板工具
│   ├── textCleaner.ts   # 文本清理工具
│   └── textFormatter.ts # 文本格式化工具
├── assets/              # 静态资源
│   └── vue.svg
└── style.css            # 全局样式
```

## 开发注意事项

### 编码规范
- 使用 `<script setup lang="ts">` 语法编写 Vue 组件
- TypeScript 配置启用了严格模式 (`strict: true`)
- 使用 `vue-tsc` 进行类型检查
- 所有组件文件使用 `.vue` 扩展名
- 所有函数需要添加注释，注释的格式为 /** */ 

```js
/**
 * @description: 加法函数
 * @param {number} a - 加数
 * @param {number} b - 加数
 * @return {number} - 和
 */
function add(a: number, b: number): number {
  return a + b;
}
```

### 文件组织规范
- **入口文件**: `src/main.ts`，挂载点为 `#app`
- **组件命名**: 所有封装的组件文件使用 PascalCase 命名，如 `About.vue`
- **页面组织**: 所有页面组件放入 `src/views/工具名称/index.vue` 文件夹
- **工具名称**: 如 `src/views/text-formatter/index.vue`
- **工具函数**: 公共方法封装到 `src/utils/` 文件夹，按功能分类
- **类型定义**: 所有 TypeScript 类型定义放入 `src/types/` 文件夹
- **API接口**: API 请求封装在 `src/api/` 文件夹，按模块分文件

### 工具开发规范
- **单一职责**: 每个工具页面只负责一个功能
- **组件粒度**: 业务组件不超过200行，工具组件不超过100行
- **Props优先**: 优先使用 props 和事件通信，避免直接操作DOM
- **组合式API**: 充分利用 Composition API 的逻辑复用能力
- **类型安全**: 所有 props、emit、ref 都要有明确的类型定义

### 性能优化规范
- **代码分割**: 路由级别使用懒加载 `() => import('./views/xxx.vue')`
- **组件缓存**: 合理使用 `KeepAlive` 缓存频繁切换的组件
- **依赖优化**: 定期分析 bundle 大小，移除未使用的依赖
- **构建优化**: 已配置手动分包，按功能模块分割代码

### 企业内部工具特点
- **安全性**: 考虑内部数据安全，避免敏感信息泄露
- **易用性**: 界面简洁直观，降低学习成本
- **稳定性**: 确保工具的可靠性和数据处理的准确性
- **扩展性**: 采用模块化设计，便于添加新工具

### 开发工作流
1. **需求分析**: 理解内部业务需求 → 技术方案评估 → 技术选型决策
2. **工具设计**: 组件结构设计 → 数据流设计 → 用户界面设计
3. **编码实现**: 遵循代码规范 → 编写工具函数 → 集成测试验证
4. **性能优化**: 性能分析 → 瓶颈识别 → 针对性优化
5. **内部测试**: 内部人员试用 → 收集反馈 → 迭代优化

### 未来工具规划
1. **文档查询工具**: 查询公司内部文档，支持搜索和分类浏览
2. **数据爬虫工具**: 使用 Puppeteer 进行百度等平台的数据爬取
3. **更多实用工具**: 根据内部需求持续扩展工具集

### 测试要求
- **单元测试**: 为复杂工具函数编写单元测试
- **功能测试**: 确保每个工具的核心功能正常工作
- **类型测试**: 利用 TypeScript 的类型系统进行编译时检查

## 部署说明

### 部署环境
- **目标环境**: 企业内部部署（静态托管或内网服务器）
- **部署方式**: 构建静态文件部署
- **访问方式**: 内网访问，无需外网连接

### 构建优化
- **代码分割**: 已按功能模块分包（vue-core、element-plus、utils等）
- **资源优化**: 图片、CSS、JS文件分别优化和命名
- **性能要求**: 页面加载时间<3秒，交互响应<100ms