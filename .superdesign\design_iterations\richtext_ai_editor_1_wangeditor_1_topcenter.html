<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>AI富文本编辑器 - wangeditor风格+AI编辑+上传Word（按钮居中上方）</title>\n  <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Inter:400,500,700&display=swap\">\n  <script src=\"https://cdn.tailwindcss.com\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/flowbite@2.0.0/dist/flowbite.min.js\"></script>\n  <script src=\"https://unpkg.com/lucide@latest/dist/umd/lucide.min.js\"></script>\n  <style>\n    body { font-family: Inter, var(--font-sans), sans-serif !important; background: #f6f7f9 !important; color: #222 !important; }\n    .editor-toolbar-wang { background: #fff; border-bottom: 1px solid #e5e7eb; padding: 0.5em 1em; display: flex; flex-wrap: wrap; gap: 0.5em; align-items: center; font-size: 15px; min-height: 48px; }\n    .editor-toolbar-wang .group { display: flex; align-items: center; gap: 0.25em; margin-right: 1.5em; }\n    .editor-toolbar-wang select, .editor-toolbar-wang button { background: transparent; border: none; color: #222; font-size: 15px; padding: 0.25em 0.5em; border-radius: 4px; cursor: pointer; transition: background 0.15s; outline: none; min-width: 32px; min-height: 32px; display: flex; align-items: center; gap: 2px; }\n    .editor-toolbar-wang button:hover, .editor-toolbar-wang select:hover { background: #f3f4f6; }\n    #uploadBtn { background: #2563eb !important; color: #fff !important; border: none !important; border-radius: 4px !important; font-weight: 500; box-shadow: 0 2px 8px 0 rgba(56,189,248,0.08); transition: background 0.15s, box-shadow 0.15s; min-width: 40px; min-height: 32px; padding: 0.25em 1em; margin-right: 1em; display: flex; align-items: center; gap: 4px; }\n    #uploadBtn:hover { background: #1d4ed8 !important; box-shadow: 0 4px 16px 0 rgba(56,189,248,0.16); }\n    .editor-wang { background: #fff; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 6px 6px; min-height: 220px; padding: 1.5em 1em 1em 1em; font-size: 16px; line-height: 1.7; color: #222; outline: none; resize: vertical; position: relative; }\n    .editor-wang:empty:before { content: '请输入内容'; color: #bdbdbd; font-size: 16px; pointer-events: none; position: absolute; }\n    .editor-wang[contenteditable=\"true\"]:focus { box-shadow: 0 0 0 2px #2563eb22; }\n    .icon-btn { width: 20px; height: 20px; display: inline-flex; align-items: center; justify-content: center; }\n    .ai-btn { background: #2563eb !important; color: #fff !important; border-radius: 6px !important; box-shadow: 0 2px 8px 0 rgba(56,189,248,0.08); transition: all 0.2s cubic-bezier(.4,0,.2,1); opacity: 0; transform: translateY(8px); pointer-events: none; position: absolute; z-index: 10; padding: 0.5em 1em; font-weight: 500; font-size: 1rem; border: none; cursor: pointer; }\n    .ai-btn.visible { opacity: 1; transform: translateY(0); pointer-events: auto; }\n    .ai-btn:hover { background: #1d4ed8 !important; transform: scale(1.08); box-shadow: 0 8px 24px rgba(56,189,248,0.12); }\n    .ai-modal { background: #fff !important; color: #222 !important; border-radius: 8px !important; box-shadow: 0 8px 32px rgba(56,189,248,0.10); border: 1px solid #e5e7eb !important; padding: 2em 1.5em; min-width: 320px; max-width: 90vw; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%) scale(1); opacity: 0; pointer-events: none; transition: all 0.25s cubic-bezier(.4,0,.2,1); z-index: 50; }\n    .ai-modal.visible { opacity: 1; pointer-events: auto; transform: translate(-50%, -50%) scale(1); }\n    .ai-modal .modal-header { font-size: 1.1rem; font-weight: 600; margin-bottom: 1em; display: flex; align-items: center; gap: 0.5em; }\n    .ai-modal textarea { width: 100%; border: 1px solid #e5e7eb; border-radius: 6px; padding: 0.75em; font-size: 1rem; margin-bottom: 1em; background: #f6f7f9; color: #222; outline: none; transition: box-shadow 0.2s; }\n    .ai-modal textarea:focus { box-shadow: 0 0 0 2px #2563eb44; border-color: #2563eb; }\n    .ai-modal .modal-actions { display: flex; gap: 1em; justify-content: flex-end; }\n    .ai-modal .btn { padding: 0.5em 1.2em; border-radius: 6px; font-weight: 500; font-size: 1rem; border: none; cursor: pointer; transition: all 0.15s; }\n    .ai-modal .btn-primary { background: #2563eb; color: #fff; }\n    .ai-modal .btn-secondary { background: #e5e7eb; color: #222; }\n    .ai-modal .btn-primary:disabled { opacity: 0.6; cursor: not-allowed; }\n    .ai-modal .loading-spinner { display: inline-block; width: 1.5em; height: 1.5em; border: 3px solid #e5e7eb; border-top: 3px solid #2563eb; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 0.5em; }\n    @keyframes spin { to { transform: rotate(360deg); } }\n    .highlighted { background: #38bdf8 !important; transition: background 0.3s; animation: highlight-fade 2s forwards; }\n    @keyframes highlight-fade { 0% { background: #38bdf8; } 80% { background: #38bdf8; } 100% { background: transparent; } }\n  </style>\n</head>\n<body>\n  <div class=\"max-w-3xl mx-auto mt-10\">\n    <div class=\"editor-toolbar-wang rounded-t-md\">\n      <button id=\"uploadBtn\" type=\"button\">\n        <i data-lucide=\"file-plus\" class=\"icon-btn\"></i> 上传Word\n      </button>\n      <div class=\"group\">\n        <select><option>正文</option><option>标题1</option><option>标题2</option></select>\n      </div>\n      <div class=\"group\">\n        <button title=\"引用\"><i data-lucide=\"quote\" class=\"icon-btn\"></i></button>\n        <button title=\"加粗\"><i data-lucide=\"bold\" class=\"icon-btn\"></i></button>\n        <button title=\"斜体\"><i data-lucide=\"italic\" class=\"icon-btn\"></i></button>\n        <button title=\"下划线\"><i data-lucide=\"underline\" class=\"icon-btn\"></i></button>\n        <button title=\"更多\"><i data-lucide=\"more-horizontal\" class=\"icon-btn\"></i></button>\n        <button title=\"字号\"><span style=\"font-size:13px;\">A▼</span></button>\n      </div>\n      <div class=\"group\">\n        <button title=\"默认字号\">默认字号</button>\n        <button title=\"默认字体\">默认字体</button>\n        <button title=\"默认行高\">默认行高</button>\n      </div>\n      <div class=\"group\">\n        <button title=\"表格\"><i data-lucide=\"table\" class=\"icon-btn\"></i></button>\n        <button title=\"代码\"><i data-lucide=\"code\" class=\"icon-btn\"></i></button>\n        <button title=\"撤销\"><i data-lucide=\"rotate-ccw\" class=\"icon-btn\"></i></button>\n        <button title=\"重做\"><i data-lucide=\"rotate-cw\" class=\"icon-btn\"></i></button>\n        <button title=\"全屏\"><i data-lucide=\"maximize\" class=\"icon-btn\"></i></button>\n      </div>\n      <div class=\"group\">\n        <button title=\"有序列表\"><i data-lucide=\"list\" class=\"icon-btn\"></i></button>\n        <button title=\"无序列表\"><i data-lucide=\"list\" class=\"icon-btn\"></i></button>\n        <button title=\"对齐\"><i data-lucide=\"align-left\" class=\"icon-btn\"></i></button>\n      </div>\n      <div class=\"group\">\n        <button title=\"表情\"><i data-lucide=\"smile\" class=\"icon-btn\"></i></button>\n        <button title=\"链接\"><i data-lucide=\"link\" class=\"icon-btn\"></i></button>\n        <button title=\"图片\"><i data-lucide=\"image\" class=\"icon-btn\"></i></button>\n      </div>\n    </div>\n    <div class=\"editor-wang\" id=\"editorWang\" contenteditable=\"true\" spellcheck=\"true\"></div>\n    <button id=\"aiEditBtn\" class=\"ai-btn\">AI编辑</button>\n    <div id=\"aiModal\" class=\"ai-modal\">\n      <div class=\"modal-header\">\n        <i data-lucide=\"sparkles\"></i> AI编辑\n      </div>\n      <textarea id=\"aiPrompt\" rows=\"3\" placeholder=\"请输入您的AI指令，如：润色这段话、改写为更正式的语气……\"></textarea>\n      <div class=\"modal-actions\">\n        <button id=\"aiSubmit\" class=\"btn btn-primary\">提交</button>\n        <button id=\"aiCancel\" class=\"btn btn-secondary\">取消</button>\n      </div>\n      <div id=\"aiLoading\" style=\"display:none;margin-top:1em;\"><span class=\"loading-spinner\"></span>AI处理中…</div>\n      <div id=\"aiError\" style=\"display:none;color:#e11d48;margin-top:1em;\">AI处理失败，请重试。</div>\n    </div>\n  </div>\n  <input type=\"file\" id=\"wordInput\" accept=\".doc,.docx\" style=\"display:none;\">\n  <script>\n    lucide.createIcons();\n    // AI编辑按钮浮现逻辑（居中于选区上方）\n    const editor = document.getElementById('editorWang');\n    const aiBtn = document.getElementById('aiEditBtn');\n    let selectionRange = null;\n    editor.addEventListener('mouseup', function(e) {\n      setTimeout(() => {\n        const sel = window.getSelection();\n        if (sel && sel.rangeCount > 0 && !sel.isCollapsed && editor.contains(sel.anchorNode)) {\n          selectionRange = sel.getRangeAt(0);\n          const rect = selectionRange.getBoundingClientRect();\n          const editorRect = editor.getBoundingClientRect();\n          // 居中于选区上方\n          aiBtn.style.top = (rect.top - editorRect.top + window.scrollY - aiBtn.offsetHeight - 8) + 'px';\n          aiBtn.style.left = (rect.left - editorRect.left + window.scrollX + (rect.width - aiBtn.offsetWidth) / 2) + 'px';\n          aiBtn.classList.add('visible');\n        } else {\n          aiBtn.classList.remove('visible');\n        }\n      }, 10);\n    });\n    document.addEventListener('mousedown', function(e) {\n      if (!aiBtn.contains(e.target) && !editor.contains(e.target)) {\n        aiBtn.classList.remove('visible');\n      }\n    });\n    // AI编辑弹窗逻辑\n    const aiModal = document.getElementById('aiModal');\n    const aiPrompt = document.getElementById('aiPrompt');\n    const aiSubmit = document.getElementById('aiSubmit');\n    const aiCancel = document.getElementById('aiCancel');\n    const aiLoading = document.getElementById('aiLoading');\n    const aiError = document.getElementById('aiError');\n    aiBtn.addEventListener('click', function() {\n      aiModal.classList.add('visible');\n      aiPrompt.value = '';\n      aiError.style.display = 'none';\n      aiLoading.style.display = 'none';\n      aiPrompt.focus();\n    });\n    aiCancel.addEventListener('click', function() {\n      aiModal.classList.remove('visible');\n    });\n    aiSubmit.addEventListener('click', function() {\n      if (!aiPrompt.value.trim()) return;\n      aiLoading.style.display = 'block';\n      aiError.style.display = 'none';\n      aiSubmit.disabled = true;\n      // 模拟AI接口调用\n      setTimeout(() => {\n        aiLoading.style.display = 'none';\n        aiSubmit.disabled = false;\n        aiModal.classList.remove('visible');\n        if (selectionRange) {\n          // 模拟AI返回内容\n          const newText = aiPrompt.value + '（AI已处理）';\n          selectionRange.deleteContents();\n          const span = document.createElement('span');\n          span.className = 'highlighted';\n          span.textContent = newText;\n          selectionRange.insertNode(span);\n          setTimeout(() => {\n            span.classList.remove('highlighted');\n          }, 2000);\n        }\n        aiBtn.classList.remove('visible');\n      }, 1800);\n    });\n    // 上传Word逻辑（仅模拟）\n    const uploadBtn = document.getElementById('uploadBtn');\n    const wordInput = document.getElementById('wordInput');\n    uploadBtn.addEventListener('click', () => wordInput.click());\n    wordInput.addEventListener('change', function(e) {\n      const file = e.target.files[0];\n      if (file) {\n        editor.innerHTML = `<h2>${file.name}</h2><p>（Word内容解析后显示在此）</p>`;\n      }\n    });\n  </script>\n</body>\n</html>\n