# CLAUDE.md

## 文档信息

| 项目名称 | utils-xjk企业内部工具集 |
|---------|----------------------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-08-20 |
| 最后更新 | 2025-08-20 |
| 文档状态 | 维护中 |
| 负责人 | 开发团队 |

## 项目概览

### 1.1 项目概述
utils-xjk是一个基于 Vue 3 + TypeScript + Vite 的企业内部工具集应用，为公司内部员工提供各种实用工具。项目采用模块化设计，支持多种工具类型的扩展。

### 1.2 业务背景
随着企业内部数字化需求的增长，员工在日常工作中面临各种重复性任务和效率瓶颈。该工具集旨在通过技术手段提升工作效率，减少重复劳动。

### 1.3 项目目标
- **主要目标**: 构建企业内部工具集，提升员工工作效率50%以上
- **质量目标**: 工具稳定性≥99%，用户满意度≥90%
- **效率目标**: 相比传统方式，任务处理时间减少60%
- **扩展目标**: 支持快速集成新工具，模块化架构设计

### 1.4 功能规划
- **当前功能**: 文本格式化、数据合并Excel生成
- **开发中**: AI文本编辑器（基于api.agicto.cn/v1）
- **未来规划**: 文档查询工具、数据爬虫工具（Puppeteer）

## 常用命令

### 开发环境
- **启动开发服务器**: `npm run dev` - 启动本地开发服务器，支持热重载
- **类型检查**: `vue-tsc -b` - 运行TypeScript类型检查
- **代码检查**: `npm run lint` - 运行ESLint代码检查
- **自动修复**: `npm run lint:fix` - 自动修复代码格式问题

### 构建和部署
- **构建项目**: `npm run build` - 构建生产版本（先运行类型检查再构建）
- **预览构建**: `npm run preview` - 预览构建结果

### 测试和调试
- **开发调试**: 在浏览器中打开开发者工具进行调试
- **构建分析**: 构建完成后可分析bundle大小和性能

## 技术栈

### 2.1 核心技术
- **前端框架**: Vue 3.5.18 (Composition API + `<script setup>`)
- **编程语言**: TypeScript ~5.8.3 (严格模式)
- **构建工具**: Vite 7.1.0
- **UI框架**: Element Plus 2.10.7
- **路由管理**: Vue Router 4.5.1
- **包管理器**: npm
- **类型检查**: vue-tsc 3.0.5

### 2.2 业务库
- **Excel处理**: xlsx 0.18.5
- **图标库**: @element-plus/icons-vue 2.3.2
- **浏览器自动化**: Puppeteer 22.15.0

### 2.3 开发工具
- **代码检查**: ESLint 8.57.0
- **Vue插件**: @vitejs/plugin-vue 6.0.1
- **TS配置**: @vue/tsconfig 0.7.0
- **ESLint插件**: eslint-plugin-vue 9.24.0
- **Vite插件**: vite-plugin-eslint 1.8.1

### 2.4 未来技术栈规划
- **AI服务**: api.agicto.cn/v1 (支持gpt-5、kimi-k2-0711-preview等模型)
- **富文本编辑**: WangEditor v5 (用于AI文本编辑器)
- **文档查询**: 待评估技术方案
- **更多工具**: 根据业务需求持续扩展

## 项目结构

### 3.1 目录结构
```
src/
├── main.ts              # 应用入口文件
├── App.vue              # 根组件
├── env.d.ts             # TypeScript环境声明
├── vite-env.d.ts        # Vite环境类型声明
├── router/              # 路由配置
│   └── index.ts         # 路由定义
├── components/          # 公共组件
├── views/               # 页面组件（按工具分类）
├── utils/               # 工具函数（按功能分类）
├── assets/              # 静态资源
└── style.css            # 全局样式
```

### 3.2 文件组织规范
- **入口文件**: `src/main.ts`，挂载点为 `#app`
- **组件命名**: 所有封装的组件文件使用 PascalCase 命名，如 `About.vue`
- **页面组织**: 所有页面组件放入 `src/views/工具名称/index.vue` 文件夹
- **工具函数**: 公共方法封装到 `src/utils/` 文件夹，按功能分类
- **类型定义**: TypeScript 类型定义放在对应文件中或创建 `src/types/` 目录
- **API接口**: API 请求封装在 `src/api/` 文件夹，按模块分文件（待扩展）

### 3.3 新工具集成规范
- **目录结构**: `src/views/工具名称/index.vue`
- **路由配置**: 在 `src/router/index.ts` 中添加新路由
- **工具函数**: 相关工具函数放入 `src/utils/` 对应分类
- **样式文件**: 工具特定样式放在工具目录下的 `styles/` 文件夹

## 开发规范

### 4.1 编码规范
- **Vue组件**: 使用 `<script setup lang="ts">` 语法编写 Vue 组件
- **TypeScript**: 严格模式 (`strict: true`)，充分利用类型系统
- **类型检查**: 使用 `vue-tsc` 进行类型检查
- **文件扩展名**: 所有组件文件使用 `.vue` 扩展名
- **函数注释**: 所有函数需要添加JSDoc格式注释

#### 注释规范示例
```typescript
/**
 * @description: 加法函数
 * @param {number} a - 加数
 * @param {number} b - 加数
 * @return {number} - 和
 */
function add(a: number, b: number): number {
  return a + b;
}
```

### 4.2 Vue组件规范
- **组件粒度**: 业务组件不超过200行，工具组件不超过100行
- **Props优先**: 优先使用 props 和事件通信，避免直接操作DOM
- **组合式API**: 充分利用 Composition API 的逻辑复用能力
- **类型安全**: 所有 props、emit、ref 都要有明确的类型定义
- **命名规范**: 组件名使用PascalCase，文件名与组件名一致

### 4.3 性能优化规范
- **代码分割**: 路由级别使用懒加载 `() => import('./views/xxx.vue')`
- **组件缓存**: 合理使用 `KeepAlive` 缓存频繁切换的组件
- **依赖优化**: 定期分析 bundle 大小，移除未使用的依赖
- **构建优化**: 已配置手动分包，按功能模块分割代码
- **性能要求**: 页面加载时间<3秒，交互响应<100ms

### 4.4 代码质量要求
- **ESLint**: 必须通过ESLint检查，代码风格一致
- **TypeScript**: 充分利用类型系统，减少any使用
- **单元测试**: 为复杂工具函数编写单元测试
- **代码审查**: 重要功能需要经过代码审查

## 开发流程

### 5.1 工具开发流程
```mermaid
flowchart TD
    A[需求分析] --> B[技术方案评估]
    B --> C[工具设计]
    C --> D[编码实现]
    D --> E[测试验证]
    E --> F[内部试用]
    F --> G[优化迭代]
    G --> H[正式发布]
    
    A -.->|1-2天| A1[理解内部业务需求]
    B -.->|1天| B1[技术选型决策]
    C -.->|1-2天| C1[组件和数据流设计]
    D -.->|2-5天| D1[遵循规范编码]
    E -.->|1天| E1[功能测试验证]
    F -.->|3-7天| F1[收集反馈优化]
```

### 5.2 工具开发规范
- **单一职责**: 每个工具页面只负责一个核心功能
- **用户中心**: 从企业内部员工角度设计易用性
- **模块化设计**: 便于后续维护和功能扩展
- **错误处理**: 完善的错误处理机制和用户提示
- **数据安全**: 确保企业内部数据不外泄

### 5.3 质量保证
- **代码审查**: 重要功能需要经过团队代码审查
- **功能测试**: 确保每个工具的核心功能正常工作
- **性能测试**: 验证工具的响应速度和资源占用
- **用户体验**: 内部人员试用，收集反馈并优化

## 部署和运维

### 6.1 部署环境
- **目标环境**: 企业内部部署（静态托管或内网服务器）
- **部署方式**: 构建静态文件部署
- **访问方式**: 内网访问，无需外网连接
- **安全要求**: 确保内部数据安全，防止未授权访问

### 6.2 构建优化
- **代码分割**: 已按功能模块分包（vue-core、element-plus、utils等）
- **资源优化**: 图片、CSS、JS文件分别优化和命名
- **性能要求**: 页面加载时间<3秒，交互响应<100ms
- **缓存策略**: 合理配置浏览器缓存策略

### 6.3 监控和维护
- **性能监控**: 监控页面加载时间和用户交互响应
- **错误追踪**: 建立错误日志收集和分析机制
- **用户反馈**: 建立用户反馈渠道，持续改进工具
- **版本管理**: 定期更新依赖包，修复安全漏洞

## 未来规划

### 7.1 短期规划（1-3个月）
- **AI文本编辑器**: 完成基于api.agicto.cn/v1的AI文本编辑功能
- **文档查询**: 开发企业内部文档查询和搜索功能
- **性能优化**: 优化现有工具的性能和用户体验

### 7.2 中期规划（3-6个月）
- **数据爬虫**: 使用Puppeteer开发数据爬虫工具
- **更多工具**: 根据内部需求添加更多实用工具

### 7.3 长期规划（6个月以上）
- **平台化**: 构建完整的企业内部工具平台
- **AI增强**: 集成更多AI功能提升工具智能化水平

## 附录

### 8.1 相关文档
- [Vue 3官方文档](https://vuejs.org/)
- [TypeScript官方文档](https://www.typescriptlang.org/)
- [Element Plus官方文档](https://element-plus.org/)
- [Vite官方文档](https://vitejs.dev/)
- [api.agicto.cn/v1 API文档](https://agicto.apifox.cn/)

### 8.2 开发工具推荐
- **IDE**: Visual Studio Code
- **浏览器**: Chrome (开发者工具)
- **版本控制**: Git
- **包管理**: npm

### 8.3 常见问题
**Q: 如何添加新工具？**  
A: 在 `src/views/` 下创建新工具目录，遵循现有目录结构，在路由配置中添加新路由。

**Q: 如何处理TypeScript类型错误？**  
A: 检查类型定义，使用 `vue-tsc -b` 进行类型检查，确保类型安全。

**Q: 如何优化构建性能？**  
A: 使用代码分割、懒加载、依赖优化等技术手段，定期分析bundle大小。

---

**文档结束**

*本文档为utils-xjk企业内部工具集的项目配置文档，所有内容需要经过开发团队确认后方可生效。*