# 文件处理架构

**文档版本**: v1.0  
**所属模块**: AI富文本编辑器架构  
**最后更新**: 2025-08-22

## 5. 文件处理架构

### 5.1 Word文档解析架构

#### 5.1.1 解析器设计 (word-parser.service.ts)

```typescript
import mammoth from 'mammoth'
import JSZip from 'jszip'

interface ParseOptions {
  includeDefaultStyleMap?: boolean
  styleMap?: string[]
  transformDocument?: (document: any) => any
}

interface ParseResult {
  html: string
  messages: Array<{
    type: 'warning' | 'error' | 'info'
    message: string
  }>
  images: Array<{
    name: string
    buffer: ArrayBuffer
    contentType: string
  }>
}

export class WordParser {
  private styleMap = [
    "p[style-name='Title'] => h1",
    "p[style-name='Subtitle'] => h2",
    "p[style-name='Heading 1'] => h1",
    "p[style-name='Heading 2'] => h2",
    "p[style-name='Heading 3'] => h3",
    "r[style-name='Strong'] => strong",
    "r[style-name='Emphasis'] => em"
  ]
  
  async parseFile(file: File, options: ParseOptions = {}): Promise<ParseResult> {
    const buffer = await file.arrayBuffer()
    
    try {
      const result = await mammoth.convertToHtml({
        buffer,
        options: {
          includeDefaultStyleMap: options.includeDefaultStyleMap !== false,
          styleMap: [...this.styleMap, ...(options.styleMap || [])],
          transformDocument: options.transformDocument
        }
      })
      
      // 处理图片
      const images = await this.extractImages(buffer)
      
      return {
        html: this.postProcessHtml(result.value),
        messages: result.messages,
        images
      }
    } catch (error) {
      throw new Error(`Word解析失败: ${error.message}`)
    }
  }
  
  private async extractImages(buffer: ArrayBuffer) {
    const zip = await JSZip.loadAsync(buffer)
    const images: Array<{name: string, buffer: ArrayBuffer, contentType: string}> = []
    
    const imageFiles = Object.keys(zip.files).filter(name => 
      /^word\/media\//.test(name) && 
      /\.(png|jpg|jpeg|gif|bmp)$/.test(name)
    )
    
    for (const imagePath of imageFiles) {
      const imageData = await zip.file(imagePath)?.async('arraybuffer')
      if (imageData) {
        images.push({
          name: imagePath.split('/').pop()!,
          buffer: imageData,
          contentType: this.getContentType(imagePath)
        })
      }
    }
    
    return images
  }
  
  private getContentType(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase()
    const typeMap = {
      png: 'image/png',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      gif: 'image/gif',
      bmp: 'image/bmp'
    }
    return typeMap[ext] || 'image/jpeg'
  }
  
  private postProcessHtml(html: string): string {
    // 清理HTML，移除不支持的标签
    return html
      .replace(/<o:p><\/o:p>/g, '') // 移除Office空段落
      .replace(/style="[^"]*mso-[^"]*"/g, '') // 移除MS Office样式
      .replace(/class="[^"]*Mso[^"]*"/g, '') // 移除MS Office类
  }
}
```

#### 5.1.2 文件上传服务 (file-upload.service.ts)

```typescript
export class FileUploadService {
  private maxFileSize = 50 * 1024 * 1024 // 50MB
  private allowedTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword'
  ]
  
  async uploadFile(file: File, onProgress?: (progress: number) => void): Promise<UploadResult> {
    this.validateFile(file)
    
    const result: UploadResult = {
      file,
      status: 'uploading',
      progress: 0,
      error: null
    }
    
    try {
      // 模拟上传进度
      if (onProgress) {
        for (let i = 0; i <= 100; i += 10) {
          await new Promise(resolve => setTimeout(resolve, 100))
          result.progress = i
          onProgress(i)
        }
      }
      
      // 解析Word文档
      const parser = new WordParser()
      const parseResult = await parser.parseFile(file)
      
      result.status = 'completed'
      result.parseResult = parseResult
      
      return result
    } catch (error) {
      result.status = 'error'
      result.error = error.message
      throw error
    }
  }
  
  private validateFile(file: File): void {
    if (!this.allowedTypes.includes(file.type)) {
      throw new Error('不支持的文件格式，请上传DOC或DOCX文件')
    }
    
    if (file.size > this.maxFileSize) {
      throw new Error(`文件大小超过限制（最大${this.maxFileSize / 1024 / 1024}MB）`)
    }
  }
}

interface UploadResult {
  file: File
  status: 'uploading' | 'completed' | 'error'
  progress: number
  error: string | null
  parseResult?: ParseResult
}
```

### 5.2 文件格式支持

#### 5.2.1 支持的文件类型

| 文件类型 | MIME类型 | 最大大小 | 处理策略 |
|----------|----------|----------|----------|
| **DOCX** | application/vnd.openxmlformats-officedocument.wordprocessingml.document | 50MB | 完整支持 |
| **DOC** | application/msword | 50MB | 兼容性处理 |
| **TXT** | text/plain | 10MB | 直接读取 |
| **MD** | text/markdown | 10MB | Markdown解析 |

#### 5.2.2 文件验证规则

```typescript
export class FileValidator {
  private rules = {
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/plain',
      'text/markdown'
    ],
    allowedExtensions: ['.docx', '.doc', '.txt', '.md']
  }
  
  validate(file: File): ValidationResult {
    const errors: string[] = []
    
    // 检查文件大小
    if (file.size > this.rules.maxSize) {
      errors.push(`文件大小不能超过${this.rules.maxSize / 1024 / 1024}MB`)
    }
    
    // 检查文件类型
    if (!this.rules.allowedTypes.includes(file.type)) {
      errors.push('不支持的文件类型')
    }
    
    // 检查文件扩展名
    const extension = '.' + file.name.split('.').pop()?.toLowerCase()
    if (!this.rules.allowedExtensions.includes(extension)) {
      errors.push('不支持的文件扩展名')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}

interface ValidationResult {
  valid: boolean
  errors: string[]
}
```

### 5.3 图片处理

#### 5.3.1 图片提取与优化

```typescript
export class ImageProcessor {
  private maxImageSize = 5 * 1024 * 1024 // 5MB
  private allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  
  async processImage(imageData: ArrayBuffer, fileName: string): Promise<ProcessedImage> {
    const blob = new Blob([imageData])
    
    // 验证图片类型
    if (!this.allowedImageTypes.includes(blob.type)) {
      throw new Error('不支持的图片格式')
    }
    
    // 压缩大图片
    if (imageData.byteLength > this.maxImageSize) {
      return await this.compressImage(blob)
    }
    
    return {
      data: imageData,
      type: blob.type,
      size: imageData.byteLength,
      name: fileName
    }
  }
  
  private async compressImage(blob: Blob): Promise<ProcessedImage> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')!
        
        // 设置压缩后的尺寸
        const maxWidth = 1920
        const maxHeight = 1080
        let { width, height } = img
        
        if (width > height && width > maxWidth) {
          height *= maxWidth / width
          width = maxWidth
        } else if (height > maxHeight) {
          width *= maxHeight / height
          height = maxHeight
        }
        
        canvas.width = width
        canvas.height = height
        ctx.drawImage(img, 0, 0, width, height)
        
        canvas.toBlob((compressedBlob) => {
          if (compressedBlob) {
            compressedBlob.arrayBuffer().then(buffer => {
              resolve({
                data: buffer,
                type: compressedBlob.type,
                size: buffer.byteLength,
                name: 'compressed_image.jpg'
              })
            })
          } else {
            reject(new Error('图片压缩失败'))
          }
        }, 'image/jpeg', 0.8)
      }
      
      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = URL.createObjectURL(blob)
    })
  }
}

interface ProcessedImage {
  data: ArrayBuffer
  type: string
  size: number
  name: string
}
```

### 5.4 错误处理与恢复

#### 5.4.1 文件处理错误类型

```typescript
export enum FileProcessingError {
  INVALID_FORMAT = 'INVALID_FORMAT',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  CORRUPTED_FILE = 'CORRUPTED_FILE',
  UNSUPPORTED_FEATURE = 'UNSUPPORTED_FEATURE',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export class FileErrorHandler {
  static getErrorMessage(errorType: FileProcessingError): string {
    const messages = {
      [FileProcessingError.INVALID_FORMAT]: '文件格式不支持',
      [FileProcessingError.FILE_TOO_LARGE]: '文件大小超出限制',
      [FileProcessingError.CORRUPTED_FILE]: '文件已损坏，无法解析',
      [FileProcessingError.UNSUPPORTED_FEATURE]: '文件包含不支持的功能',
      [FileProcessingError.NETWORK_ERROR]: '网络连接错误，请重试',
      [FileProcessingError.UNKNOWN_ERROR]: '处理文件时发生未知错误'
    }
    
    return messages[errorType] || messages[FileProcessingError.UNKNOWN_ERROR]
  }
  
  static isRecoverable(errorType: FileProcessingError): boolean {
    const recoverableErrors = [
      FileProcessingError.NETWORK_ERROR,
      FileProcessingError.UNSUPPORTED_FEATURE
    ]
    
    return recoverableErrors.includes(errorType)
  }
}
```