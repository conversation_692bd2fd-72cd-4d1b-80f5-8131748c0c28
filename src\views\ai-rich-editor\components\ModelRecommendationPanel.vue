<template>
  <div class="model-recommendation-panel">
    <div class="recommendation-header">
      <h3>智能模型推荐</h3>
      <p class="recommendation-desc">基于您的内容特点，为您推荐最适合的AI模型</p>
    </div>

    <!-- 推荐上下文信息 -->
    <div class="context-info" v-if="currentContext">
      <el-descriptions :column="2" border size="small">
        <el-descriptions-item label="内容类型">
          <el-tag size="small">{{ currentContext.contentType }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="文本长度">
          {{ currentContext.textLength }} 字符
        </el-descriptions-item>
        <el-descriptions-item label="紧急程度">
          <el-tag 
            :type="getUrgencyType(currentContext.urgency)" 
            size="small"
          >
            {{ getUrgencyLabel(currentContext.urgency) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="质量要求">
          <el-tag 
            :type="getQualityType(currentContext.qualityRequirement)" 
            size="small"
          >
            {{ getQualityLabel(currentContext.qualityRequirement) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 推荐模型列表 -->
    <div class="recommended-models">
      <h4>推荐模型</h4>
      <div class="model-cards">
        <div 
          v-for="(recommendation, index) in rankedRecommendations" 
          :key="recommendation.model.id"
          class="model-card"
          :class="{ 'top-recommendation': index === 0 }"
          @click="selectModel(recommendation.model.id)"
        >
          <!-- 推荐排名 -->
          <div class="recommendation-rank">
            <el-tag 
              :type="index === 0 ? 'success' : 'info'" 
              effect="dark"
              size="small"
            >
              {{ index === 0 ? '最佳' : `推荐 ${index + 1}` }}
            </el-tag>
          </div>

          <!-- 模型信息 -->
          <div class="model-info">
            <div class="model-header">
              <h5>{{ recommendation.model.name }}</h5>
              <el-tag 
                :type="getCategoryColor(recommendation.model.category)" 
                size="small"
              >
                {{ getCategoryLabel(recommendation.model.category) }}
              </el-tag>
            </div>
            
            <p class="model-description">{{ recommendation.model.description }}</p>
            
            <!-- 推荐理由 -->
            <div class="recommendation-reasons">
              <div 
                v-for="reason in recommendation.reasons" 
                :key="reason"
                class="reason-item"
              >
                <el-icon><Check /></el-icon>
                <span>{{ reason }}</span>
              </div>
            </div>

            <!-- 性能指标 -->
            <div class="performance-indicators">
              <div class="indicator">
                <span class="label">响应时间:</span>
                <span class="value">{{ recommendation.model.responseTime }}</span>
              </div>
              <div class="indicator">
                <span class="label">质量评分:</span>
                <el-rate 
                  :model-value="recommendation.model.qualityRating" 
                  disabled 
                  size="small"
                />
              </div>
            </div>

            <!-- 适用场景 -->
            <div class="use-cases">
              <span class="label">适用场景:</span>
              <div class="case-tags">
                <el-tag 
                  v-for="useCase in recommendation.model.bestFor" 
                  :key="useCase"
                  type="info"
                  effect="plain"
                  size="small"
                >
                  {{ useCase }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 选择按钮 -->
          <div class="select-action">
            <el-button 
              type="primary" 
              size="small"
              :plain="index !== 0"
              @click.stop="selectModel(recommendation.model.id)"
            >
              使用此模型
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 无推荐结果 -->
    <div v-if="rankedRecommendations.length === 0" class="no-recommendations">
      <el-empty description="暂无可推荐的模型" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAIStore } from '../stores/ai'
import type { AIModel, ModelRecommendationContext, ModelCategory } from '../types/ai.types'
import { Check } from '@element-plus/icons-vue'

interface Props {
  recommendedModels: AIModel[]
  currentContext: ModelRecommendationContext
}

interface Recommendation {
  model: AIModel
  score: number
  reasons: string[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  selectModel: [modelId: string]
}>()

const aiStore = useAIStore()

// 计算推荐得分和理由
const rankedRecommendations = computed((): Recommendation[] => {
  if (!props.currentContext) return []

  const recommendations: Recommendation[] = props.recommendedModels.map(model => {
    const { score, reasons } = calculateRecommendationScore(model, props.currentContext)
    return {
      model,
      score,
      reasons
    }
  })

  // 按得分排序
  return recommendations.sort((a, b) => b.score - a.score)
})

// 计算推荐得分
function calculateRecommendationScore(
  model: AIModel, 
  context: ModelRecommendationContext
): { score: number; reasons: string[] } {
  let score = 0
  const reasons: string[] = []

  // 1. 内容类型匹配 (权重: 40%)
  if (context.contentType.includes('中文') && model.category === 'chinese' as AIModel['category']) {
    score += 40
    reasons.push('针对中文内容优化')
  } else if (context.contentType.includes('英文') && model.category === 'multilingual' as AIModel['category']) {
    score += 30
    reasons.push('多语言支持能力强')
  }

  // 2. 质量要求匹配 (权重: 30%)
  if (context.qualityRequirement === 'high' && model.category === 'quality') {
    score += 30
    reasons.push('高质量输出保证')
  } else if (context.qualityRequirement === 'medium' && model.category === 'speed') {
    score += 20
    reasons.push('平衡质量与速度')
  }

  // 3. 紧急程度匹配 (权重: 20%)
  if (context.urgency === 'high' && model.category === 'speed') {
    score += 20
    reasons.push('快速响应需求')
  } else if (context.urgency === 'low' && model.category === 'quality') {
    score += 15
    reasons.push('适合深度处理')
  }

  // 4. 文本长度适配 (权重: 10%)
  if (context.textLength < 100 && model.category === 'speed') {
    score += 10
    reasons.push('适合短文本处理')
  } else if (context.textLength > 500 && model.category === 'quality') {
    score += 10
    reasons.push('适合长文本处理')
  }

  // 5. 基础质量评分加成
  score += model.qualityRating * 2

  return { score, reasons }
}

// 选择模型
function selectModel(modelId: string) {
  emit('selectModel', modelId)
}

// 获取标签颜色
function getCategoryColor(category: string): string {
  return aiStore.getModelCategoryColor(category as ModelCategory)
}

function getCategoryLabel(category: string): string {
  return aiStore.getModelCategoryLabel(category as ModelCategory)
}

function getUrgencyType(urgency: string): string {
  const map = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return map[urgency as keyof typeof map] || 'info'
}

function getUrgencyLabel(urgency: string): string {
  const map = {
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return map[urgency as keyof typeof map] || urgency
}

function getQualityType(quality: string): string {
  const map = {
    'high': 'success',
    'medium': 'warning',
    'low': 'info'
  }
  return map[quality as keyof typeof map] || 'info'
}

function getQualityLabel(quality: string): string {
  const map = {
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return map[quality as keyof typeof map] || quality
}
</script>

<style scoped>
.model-recommendation-panel {
  padding: 20px;
}

.recommendation-header {
  margin-bottom: 20px;
  text-align: center;
}

.recommendation-header h3 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.recommendation-desc {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.context-info {
  margin-bottom: 24px;
}

.recommended-models h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

.model-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.model-card {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.model-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.model-card.top-recommendation {
  border-color: var(--el-color-success);
  background-color: var(--el-color-success-light-9);
}

.recommendation-rank {
  position: absolute;
  top: -8px;
  right: 12px;
}

.model-info {
  margin-bottom: 12px;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.model-header h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.model-description {
  margin: 8px 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.recommendation-reasons {
  margin: 12px 0;
}

.reason-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  font-size: 13px;
  color: var(--el-color-success);
}

.reason-item .el-icon {
  font-size: 12px;
}

.performance-indicators {
  display: flex;
  gap: 16px;
  margin: 12px 0;
  font-size: 13px;
}

.indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.indicator .label {
  color: var(--el-text-color-secondary);
}

.indicator .value {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.use-cases {
  margin: 12px 0;
}

.use-cases .label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  color: var(--el-text-color-secondary);
}

.case-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.select-action {
  text-align: center;
}

.no-recommendations {
  text-align: center;
  padding: 40px 0;
}

@media (max-width: 768px) {
  .model-recommendation-panel {
    padding: 16px;
  }
  
  .performance-indicators {
    flex-direction: column;
    gap: 8px;
  }
  
  .model-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}
</style>