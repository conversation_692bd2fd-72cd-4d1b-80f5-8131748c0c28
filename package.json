{"name": "utils", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.ts", "lint:fix": "eslint src --ext .vue,.js,.ts --fix", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "docx": "^9.5.1", "element-plus": "^2.10.7", "file-saver": "^2.0.5", "jszip": "^3.10.1", "mammoth": "^1.6.0", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.24.0", "jsdom": "^26.1.0", "sass-embedded": "^1.90.0", "typescript": "~5.8.3", "vite": "^7.1.0", "vite-plugin-eslint": "^1.8.1", "vitest": "^3.2.4", "vue-tsc": "^3.0.5"}}