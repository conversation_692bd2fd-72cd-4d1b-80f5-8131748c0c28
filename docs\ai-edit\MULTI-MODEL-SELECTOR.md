# 多模型AI选择器 - 功能文档

## 概述
多模型AI选择器是AI富文本编辑器的核心功能之一，允许用户根据不同的写作场景选择最适合的AI模型，以获得最佳的写作建议和质量。

## 功能特性

### 1. 支持模型
- **GPT-3.5 Turbo** - 快速响应，适合日常写作
- **GPT-4 Turbo** - 高质量输出，适合专业内容
- **Kimi K2** - 中文优化，本土化表达
- **文心一言** - 百度AI，中文商务场景
- **通义千问** - 阿里AI，多语言支持

### 2. 智能推荐系统
基于以下因素智能推荐模型：
- **内容类型** - 项目计划书、商务邮件、工作报告等
- **文本长度** - 短文本、中等长度、长文档
- **紧急程度** - 高、中、低
- **质量要求** - 高、中、低
- **语言类型** - 中文、英文、多语言

### 3. 性能监控
实时跟踪各模型性能指标：
- 平均响应时间
- 成功率统计
- 使用频率分析
- 错误计数跟踪

## 技术架构

### 组件结构
```
src/views/ai-rich-editor/
├── components/
│   ├── ModelSelector.vue          # 模型选择器主组件
│   ├── ModelDetailsPanel.vue      # 模型详情面板
│   └── ModelRecommendationPanel.vue # 智能推荐面板
├── stores/
│   └── ai.ts                     # 状态管理
└── types/
    └── ai.types.ts               # 类型定义
```

### 核心接口
```typescript
interface AIModel {
  id: string
  name: string
  description: string
  category: ModelCategory
  responseTime: string
  qualityRating: number
  bestFor: string[]
  isActive: boolean
  provider: string
}

interface ModelRecommendationContext {
  contentType: string
  textLength: number
  urgency: 'high' | 'medium' | 'low'
  qualityRequirement: 'high' | 'medium' | 'low'
  language: string
  useCase: string
}
```

## 使用方法

### 1. 手动选择模型
1. 点击编辑器顶部工具栏的模型选择按钮
2. 从下拉菜单中选择合适的AI模型
3. 查看模型详情可点击"详情"按钮

### 2. 智能推荐
1. 点击工具栏中的"智能推荐"按钮（魔法棒图标）
2. 系统分析当前内容并推荐最适合的模型
3. 选择推荐结果中的任一模型使用

### 3. 键盘快捷键
- `Ctrl/Cmd + M` - 打开模型选择器
- `Ctrl/Cmd + R` - 智能推荐模型

## 推荐算法

### 评分机制
算法基于以下权重计算推荐得分：

| 因素 | 权重 | 说明 |
|------|------|------|
| 内容类型匹配 | 40% | 基于内容关键词分析 |
| 质量要求匹配 | 30% | 根据质量需求选择模型 |
| 紧急程度匹配 | 20% | 响应时间vs质量平衡 |
| 文本长度适配 | 10% | 模型对文本长度的适应性 |

### 推荐实例

#### 场景1：项目计划书
- **内容类型**：项目计划书
- **推荐模型**：GPT-4 Turbo
- **理由**：高质量输出，适合专业内容

#### 场景2：快速邮件回复
- **内容类型**：商务邮件
- **推荐模型**：GPT-3.5 Turbo
- **理由**：快速响应，适合日常写作

#### 场景3：中文商务文档
- **内容类型**：中文商务文档
- **推荐模型**：Kimi K2
- **理由**：针对中文内容优化

## 性能数据

### 基准性能
| 模型 | 响应时间 | 质量评分 | 适用场景 |
|------|----------|----------|----------|
| GPT-3.5 Turbo | <1秒 | 3.5/5 | 日常写作 |
| GPT-4 Turbo | 2-3秒 | 5.0/5 | 专业内容 |
| Kimi K2 | 1-2秒 | 4.5/5 | 中文优化 |
| 文心一言 | 2-3秒 | 4.2/5 | 中文商务 |
| 通义千问 | 2-3秒 | 4.0/5 | 多语言支持 |

### 实时统计
系统会实时更新每个模型的：
- 平均响应时间
- 成功率
- 使用频率
- 错误统计

## 自定义配置

### 添加新模型
1. 在 `ai.types.ts` 中添加新模型配置
2. 更新 `AI_MODELS` 常量
3. 添加模型分类标签和颜色配置

### 调整推荐算法
1. 修改 `ModelRecommendationPanel.vue` 中的评分逻辑
2. 调整权重分配
3. 更新推荐理由文案

## 故障排除

### 常见问题

#### 1. 模型选择器不显示
- 检查网络连接
- 确认API密钥配置正确
- 查看浏览器控制台错误信息

#### 2. 推荐不准确
- 检查内容识别逻辑
- 验证关键词匹配规则
- 查看推荐得分计算

#### 3. 性能统计异常
- 清除浏览器缓存
- 重置性能数据
- 检查网络延迟

### 调试工具

#### 开发者工具
在浏览器控制台中使用以下命令调试：

```javascript
// 查看当前模型
const aiStore = useAIStore()
console.log(aiStore.currentModel)

// 查看性能统计
console.log(aiStore.performanceMetrics)

// 测试推荐算法
const testContext = {
  contentType: '中文商务文档',
  textLength: 200,
  urgency: 'medium',
  qualityRequirement: 'high',
  language: '中文',
  useCase: '商务写作'
}
console.log(aiStore.recommendedModels(testContext))
```

## 扩展计划

### 即将推出
- [ ] 自定义模型配置
- [ ] A/B测试功能
- [ ] 团队协作设置
- [ ] 高级统计分析

### 未来功能
- [ ] 模型性能图表
- [ ] 批量模型测试
- [ ] 智能模型切换
- [ ] 用户偏好学习

## 技术规格

### 依赖要求
- Vue 3.5.18+
- Element Plus 2.10.7+
- Pinia 2.1+
- TypeScript 5.8.3+

### 性能要求
- 模型切换响应时间 <500ms
- 推荐算法执行时间 <100ms
- UI渲染时间 <50ms
- 内存占用 <10MB额外开销

## 更新日志

### v1.0.0 (2025-08-24)
- 初始版本发布
- 支持5个AI模型选择
- 实现智能推荐系统
- 添加性能监控功能
- 集成到AI富文本编辑器

---

**维护者**: 开发团队  
**最后更新**: 2025-08-24  
**状态**: 生产环境稳定运行