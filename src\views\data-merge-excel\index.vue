<template>
  <div class="data-merge-container">
    <el-card class="upload-card">
      <template #header>
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>数据合并与Excel生成工具</span>
        </div>
      </template>
      
      <!-- 平台选择 -->
      <div class="platform-section">
        <label class="platform-label">
          <el-icon><Platform /></el-icon>
          选择平台
        </label>
        <el-radio-group v-model="selectedPlatform" class="platform-options" @change="handlePlatformChange">
          <el-radio label="xiaohongshu" border>
            <el-icon><Star /></el-icon>
            小红书
          </el-radio>
          <el-radio label="zhihu" border>
            <el-icon><InfoFilled /></el-icon>
            知乎
          </el-radio>
          <el-radio label="bilibili" border>
            <el-icon><VideoPlay /></el-icon>
            B站
          </el-radio>
        </el-radio-group>
      </div>
      
      <div class="upload-section">
        <div class="upload-item">
          <label class="upload-label">
            <el-icon><FolderOpened /></el-icon>
            评论数据文件 ({{ getCommentsFileName() }})
          </label>
          <el-upload
            ref="commentsUploadRef"
            class="upload-area"
            drag
            :auto-upload="false"
            :show-file-list="false"
            accept=".json"
            :limit="1"
            :on-exceed="handleExceed"
            @change="handleCommentsFileChange"
          >
            <el-icon class="el-icon--upload"><Upload /></el-icon>
            <div class="el-upload__text">
              拖拽文件到此处或 <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传JSON格式的评论数据文件
              </div>
            </template>
          </el-upload>
          
          <!-- 显示已选择的文件 -->
          <div v-if="commentsFile" class="selected-file">
            <el-icon><Document /></el-icon>
            <span>{{ commentsFile.name }}</span>
            <el-icon class="remove-file" @click="removeCommentsFile"><Close /></el-icon>
          </div>
        </div>

        <div class="upload-item">
          <label class="upload-label">
            <el-icon><FolderOpened /></el-icon>
            内容数据文件 ({{ getContentsFileName() }})
          </label>
          <el-upload
            ref="contentsUploadRef"
            class="upload-area"
            drag
            :auto-upload="false"
            :show-file-list="false"
            accept=".json"
            :limit="1"
            :on-exceed="handleExceed"
            @change="handleContentsFileChange"
          >
            <el-icon class="el-icon--upload"><Upload /></el-icon>
            <div class="el-upload__text">
              拖拽文件到此处或 <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传JSON格式的内容数据文件
              </div>
            </template>
          </el-upload>
          
          <!-- 显示已选择的文件 -->
          <div v-if="contentsFile" class="selected-file">
            <el-icon><Document /></el-icon>
            <span>{{ contentsFile.name }}</span>
            <el-icon class="remove-file" @click="removeContentsFile"><Close /></el-icon>
          </div>
        </div>
      </div>

      <div class="action-section">
        <el-button
          type="primary"
          size="large"
          :loading="processing"
          :disabled="!commentsFile || !contentsFile || (selectedPlatform === 'bilibili')"
          @click="processData"
        >
          <el-icon><Magic /></el-icon>
          处理数据并生成Excel
        </el-button>
        <div v-if="selectedPlatform === 'bilibili'" class="platform-hint">
          <el-alert
            title="功能开发中"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              {{ getPlatformHint() }}
            </template>
          </el-alert>
        </div>
      </div>

      <div v-if="showProgress" class="progress-section">
        <el-progress :percentage="progress" :status="progressStatus" />
        <div class="progress-text">{{ progressText }}</div>
      </div>

      <div v-if="result" class="result-section">
        <el-alert
          :title="result.message"
          :type="result.success ? 'success' : 'error'"
          :closable="false"
          show-icon
        />
        <div v-if="result.success && result.stats" class="stats-info">
          <h4>处理统计：</h4>
          <ul>
            <li>原始内容数量: {{ result.stats.originalContents }}</li>
            <li>去重后内容数量: {{ result.stats.uniqueContents }}</li>
            <li>总评论数: {{ result.stats.totalComments }}</li>
            <li>总回复数: {{ result.stats.totalReplies }}</li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { ElUpload } from 'element-plus'
import { 
  Document, 
  Upload, 
  Close, 
  MagicStick as Magic, 
  FolderOpened, 
  Monitor as Platform, 
  Star, 
  InfoFilled, 
  VideoPlay 
} from '@element-plus/icons-vue'

// 通用评论接口
interface BaseComment {
  comment_id: string | number;
  content: string
  user_nickname: string
  create_time: number
  like_count: number
  parent_comment_id: string | number;
  children?: BaseComment[]
}

// 小红书评论接口
interface XiaohongshuComment extends BaseComment {
  comment_id: number
  parent_comment_id: number
  note_id: number
}

// 知乎评论接口
interface ZhihuComment extends BaseComment {
  content_id: string
  content_type: string
  user_id: string
  ip_location?: string
  sub_comment_count: number
  dislike_count: number
}

// B站评论接口
interface BilibiliComment extends BaseComment {
  // 待定义
}

type Comment = XiaohongshuComment | ZhihuComment | BilibiliComment

// 通用内容接口
interface BaseContent {
  id: string | number;
  title: string
  desc: string
  user_nickname: string
  like_count: number
  time: number
  url?: string
  comments?: Comment[]
  comment_count?: number
}

// 小红书内容接口
interface XiaohongshuContent extends BaseContent {
  id: number
  note_id: number
  note_url?: string
  liked_count: number
}

// 知乎内容接口
interface ZhihuContent extends BaseContent {
  content_id: string
  content_type: string
  content_text: string
  content_url: string
  question_id: string
  voteup_count: number
  user_id: string
  user_link: string
  user_url_token: string
}

// B站内容接口
interface BilibiliContent extends BaseContent {
  // 待定义
}

type Content = XiaohongshuContent | ZhihuContent | BilibiliContent

type PlatformType = 'xiaohongshu' | 'zhihu' | 'bilibili'

interface ProcessResult {
  success: boolean
  message: string
  stats?: {
    originalContents: number
    uniqueContents: number
    totalComments: number
    totalReplies: number
  }
}

const selectedPlatform = ref<PlatformType>('xiaohongshu')
const commentsFile = ref<File | null>(null)
const contentsFile = ref<File | null>(null)
const commentsUploadRef = ref<InstanceType<typeof ElUpload>>()
const contentsUploadRef = ref<InstanceType<typeof ElUpload>>()
const processing = ref(false)
const showProgress = ref(false)
const progress = ref(0)
const progressStatus = ref('')
const progressText = ref('')
const result = ref<ProcessResult | null>(null)

/**
 * @description: 处理评论文件选择
 * @param {any} file - 文件对象
 */
const handleCommentsFileChange = (file: any) => {
  commentsFile.value = file.raw
  result.value = null
}

/**
 * @description: 处理内容文件选择
 * @param {any} file - 文件对象
 */
const handleContentsFileChange = (file: any) => {
  contentsFile.value = file.raw
  result.value = null
}

/**
 * @description: 获取评论文件名
 */
const getCommentsFileName = () => {
  const fileNames = {
    xiaohongshu: 'search_comments.json',
    zhihu: 'zhihu_answers.json',
    bilibili: 'bilibili_comments.json'
  }
  return fileNames[selectedPlatform.value]
}

/**
 * @description: 获取内容文件名
 */
const getContentsFileName = () => {
  const fileNames = {
    xiaohongshu: 'search_contents.json',
    zhihu: 'zhihu_questions.json',
    bilibili: 'bilibili_videos.json'
  }
  return fileNames[selectedPlatform.value]
}

/**
 * @description: 处理文件超出限制的情况
 */
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件，请先移除已选择的文件')
}

/**
 * @description: 移除评论文件
 */
const removeCommentsFile = () => {
  commentsFile.value = null
  result.value = null
  // 清空上传组件内部的文件列表
  commentsUploadRef.value?.clearFiles()
}

/**
 * @description: 移除内容文件
 */
const removeContentsFile = () => {
  contentsFile.value = null
  result.value = null
  // 清空上传组件内部的文件列表
  contentsUploadRef.value?.clearFiles()
}

/**
 * @description: 获取平台提示信息
 */
const getPlatformHint = () => {
  const hints = {
    zhihu: '知乎数据处理功能正在开发中，暂时无法使用',
    bilibili: 'B站数据处理功能正在开发中，暂时无法使用',
    xiaohongshu: ''
  }
  return hints[selectedPlatform.value] || ''
}

/**
 * @description: 处理平台切换
 */
const handlePlatformChange = () => {
  // 切换平台时清空已选择的文件
  commentsFile.value = null
  contentsFile.value = null
  result.value = null
  commentsUploadRef.value?.clearFiles()
  contentsUploadRef.value?.clearFiles()
}

// 读取JSON文件
const readJsonFile = (file: File): Promise<any> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string)
        resolve(data)
      } catch (error) {
        reject(new Error(`文件 ${file.name} 格式错误: ${error}`))
      }
    }
    reader.onerror = () => reject(new Error(`读取文件 ${file.name} 失败`))
    reader.readAsText(file)
  })
}

// 构建评论层级结构
const buildCommentHierarchy = (comments: Comment[]): Comment[] => {
  const commentMap = new Map<string, Comment>()
  const rootComments: Comment[] = []

  // 第一遍：创建所有评论的映射
  comments.forEach(comment => {
    const commentId = typeof comment.comment_id === 'number' 
      ? comment.comment_id.toString() 
      : comment.comment_id
    commentMap.set(commentId, {
      ...comment,
      children: []
    })
  })

  // 第二遍：构建层级关系
  comments.forEach(comment => {
    const commentId = typeof comment.comment_id === 'number' 
      ? comment.comment_id.toString() 
      : comment.comment_id
    const commentNode = commentMap.get(commentId)
    
    const parentId = typeof comment.parent_comment_id === 'number' 
      ? comment.parent_comment_id.toString() 
      : comment.parent_comment_id
    
    if (parentId === '0') {
      // 顶级评论
      rootComments.push(commentNode!)
    } else {
      // 子评论，添加到父级的children中
      const parentComment = commentMap.get(parentId)
      if (parentComment) {
        parentComment.children!.push(commentNode!)
      } else {
        console.warn(`找不到父级评论 ${parentId}，将作为顶级评论处理`)
        rootComments.push(commentNode!)
      }
    }
  })

  return rootComments
}

// 平台特定的数据转换函数
const transformPlatformData = (contents: any[], comments: any[]) => {
  switch (selectedPlatform.value) {
    case 'xiaohongshu':
      return transformXiaohongshuData(contents, comments)
    case 'zhihu':
      return transformZhihuData(contents, comments)
    case 'bilibili':
      return transformBilibiliData(contents, comments)
    default:
      return transformXiaohongshuData(contents, comments)
  }
}

// 小红书数据转换
const transformXiaohongshuData = (contents: any[], comments: any[]) => {
  const commentMap = new Map<number, Comment[]>()
  
  // 创建评论映射，按note_id分组
  comments.forEach(comment => {
    if (!commentMap.has(comment.note_id)) {
      commentMap.set(comment.note_id, [])
    }
    commentMap.get(comment.note_id)!.push(comment)
  })

  // 去除重复的笔记ID，只保留每个ID的第一个出现项
  const uniqueContents: Content[] = []
  const seenNoteIds = new Set<number>()
  
  contents.forEach(content => {
    if (!seenNoteIds.has(content.note_id)) {
      seenNoteIds.add(content.note_id)
      uniqueContents.push(content)
    }
  })

  // 合并到内容中
  return uniqueContents.map(content => {
    const xiaohongshuContent = content as XiaohongshuContent
    const contentComments = commentMap.get(xiaohongshuContent.note_id) || []
    const hierarchicalComments = buildCommentHierarchy(contentComments)
    
    return {
      ...content,
      comments: hierarchicalComments,
      comment_count: hierarchicalComments.length
    }
  })
}

// 知乎数据转换
const transformZhihuData = (contents: any[], comments: any[]) => {
  const commentMap = new Map<string, ZhihuComment[]>()
  
  // 创建评论映射，按content_id分组
  comments.forEach(comment => {
    if (!commentMap.has(comment.content_id)) {
      commentMap.set(comment.content_id, [])
    }
    commentMap.get(comment.content_id)!.push(comment)
  })

  // 去除重复的内容ID，只保留每个ID的第一个出现项
  const uniqueContents: ZhihuContent[] = []
  const seenContentIds = new Set<string>()
  
  contents.forEach(content => {
    if (!seenContentIds.has(content.content_id)) {
      seenContentIds.add(content.content_id)
      uniqueContents.push(content)
    }
  })

  // 合并到内容中
  return uniqueContents.map(content => {
    const contentComments = commentMap.get(content.content_id) || []
    const hierarchicalComments = buildCommentHierarchy(contentComments)
    
    return {
      ...content,
      id: content.content_id,
      like_count: content.voteup_count,
      url: content.content_url,
      comments: hierarchicalComments,
      comment_count: hierarchicalComments.length
    }
  })
}

// B站数据转换（待实现）
const transformBilibiliData = (_contents: any[], _comments: any[]) => {
  // TODO: 实现B站数据格式转换逻辑
  return []
}

// 合并内容和评论，并去除重复的笔记ID
const mergeContentWithComments = (contents: Content[], comments: Comment[]): Content[] => {
  return transformPlatformData(contents, comments)
}

// 时间戳转换为可读日期
const timestampToDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 按照平台格式添加内容行
const addContentRow = (worksheet: any, content: Content) => {
  if (selectedPlatform.value === 'xiaohongshu') {
    const xiaohongshuContent = content as XiaohongshuContent
    const row = [
      xiaohongshuContent.note_id,
      xiaohongshuContent.title,
      xiaohongshuContent.desc,
      xiaohongshuContent.user_nickname,
      xiaohongshuContent.liked_count,
      xiaohongshuContent.comment_count,
      timestampToDate(xiaohongshuContent.time),
      xiaohongshuContent.note_url || '',
      '', '', '', '', '', '', '', '', '', '', ''
    ]
    worksheet.push(row)
  } else if (selectedPlatform.value === 'zhihu') {
    const zhihuContent = content as ZhihuContent
    const row = [
      zhihuContent.question_id,
      zhihuContent.title,
      zhihuContent.desc,
      zhihuContent.user_nickname,
      zhihuContent.voteup_count,
      zhihuContent.comment_count,
      timestampToDate(zhihuContent.time),
      zhihuContent.content_url,
      '', '', '', '', '', '', '', '', '', '', ''
    ]
    worksheet.push(row)
  }
}

// 按照平台格式添加一级评论行
const addLevel1CommentRow = (worksheet: any, comment: Comment) => {
  if (selectedPlatform.value === 'xiaohongshu') {
    const xiaohongshuComment = comment as XiaohongshuComment
    const row = [
      '', '', '', '', '', '', '', '',
      xiaohongshuComment.comment_id,
      xiaohongshuComment.content,
      xiaohongshuComment.user_nickname,
      timestampToDate(xiaohongshuComment.create_time),
      xiaohongshuComment.like_count,
      '', '', '', '', ''
    ]
    worksheet.push(row)
  } else if (selectedPlatform.value === 'zhihu') {
    const zhihuComment = comment as ZhihuComment
    const row = [
      '', '', '', '', '', '', '', '',
      zhihuComment.comment_id,
      zhihuComment.content,
      zhihuComment.user_nickname,
      timestampToDate(zhihuComment.create_time),
      zhihuComment.like_count,
      '', '', '', '', ''
    ]
    worksheet.push(row)
  }
}

// 按照平台格式添加二级评论行
const addLevel2CommentRow = (worksheet: any, comment: Comment) => {
  if (selectedPlatform.value === 'xiaohongshu') {
    const xiaohongshuComment = comment as XiaohongshuComment
    const row = [
      '', '', '', '', '', '', '', '',
      '', '', '', '', '',
      xiaohongshuComment.comment_id,
      xiaohongshuComment.content,
      xiaohongshuComment.user_nickname,
      timestampToDate(xiaohongshuComment.create_time),
      xiaohongshuComment.like_count
    ]
    worksheet.push(row)
  } else if (selectedPlatform.value === 'zhihu') {
    const zhihuComment = comment as ZhihuComment
    const row = [
      '', '', '', '', '', '', '', '',
      '', '', '', '', '',
      zhihuComment.comment_id,
      zhihuComment.content,
      zhihuComment.user_nickname,
      timestampToDate(zhihuComment.create_time),
      zhihuComment.like_count
    ]
    worksheet.push(row)
  }
}

// 递归处理评论层级
const processCommentHierarchy = (worksheet: any, comment: Comment, level: number = 1) => {
  if (level === 1) {
    // 添加一级评论内容
    addLevel1CommentRow(worksheet, comment)
    
    // 处理子评论
    if (comment.children && comment.children.length > 0) {
      comment.children.forEach(childComment => {
        processCommentHierarchy(worksheet, childComment, level + 1)
      })
    }
  } else if (level === 2) {
    // 添加二级评论内容
    addLevel2CommentRow(worksheet, comment)
    
    // 处理更深层的子评论（也作为二级评论显示）
    if (comment.children && comment.children.length > 0) {
      comment.children.forEach(childComment => {
        processCommentHierarchy(worksheet, childComment, level + 1)
      })
    }
  }
}

// 获取平台特定的Excel标题
const getPlatformHeaders = () => {
  const headers = {
    xiaohongshu: [
      '笔记ID', '标题', '描述', '作者', '点赞数', '评论数', '创建时间', '链接',
      '评论ID(1级评论)', '评论内容', '评论作者', '评论时间', '评论点赞',
      '评论ID(二级评论)', '评论内容', '评论作者', '评论时间', '评论点赞'
    ],
    zhihu: [
      '问题ID', '问题标题', '问题描述', '提问者', '关注数', '回答数', '创建时间', '链接',
      '回答ID', '回答内容', '回答作者', '回答时间', '点赞数',
      '评论ID', '评论内容', '评论作者', '评论时间', '点赞数'
    ],
    bilibili: [
      '视频ID', '视频标题', '视频描述', 'UP主', '点赞数', '评论数', '发布时间', '链接',
      '评论ID(1级评论)', '评论内容', '评论作者', '评论时间', '点赞数',
      '评论ID(二级评论)', '评论内容', '评论作者', '评论时间', '点赞数'
    ]
  }
  return headers[selectedPlatform.value]
}

// 生成Excel工作簿
const createExcelWorkbook = (mergedData: Content[]): any => {
  const worksheet: any[] = []
  
  // 添加标题行
  const headers = getPlatformHeaders()
  worksheet.push(headers)
  
  // 为每个内容添加数据
  mergedData.forEach((content, contentIndex) => {
    // 添加主体内容行
    addContentRow(worksheet, content)
    
    // 添加评论
    if (content.comments && content.comments.length > 0) {
      content.comments.forEach(comment => {
        processCommentHierarchy(worksheet, comment, 1)
      })
    }
    
    // 添加分隔行（可选）
    if (contentIndex < mergedData.length - 1) {
      const separatorRow = new Array(18).fill('')
      worksheet.push(separatorRow)
    }
  })
  
  return worksheet
}

// 处理数据并生成Excel
const processData = async () => {
  if (!commentsFile.value || !contentsFile.value) {
    ElMessage.warning('请先上传两个必需的文件')
    return
  }

  processing.value = true
  showProgress.value = true
  progress.value = 0
  progressStatus.value = ''
  progressText.value = '正在读取文件...'
  result.value = null

  try {
    // 读取文件
    progress.value = 20
    progressText.value = '正在读取评论数据...'
    const comments = await readJsonFile(commentsFile.value)
    
    progress.value = 40
    progressText.value = '正在读取内容数据...'
    const contents = await readJsonFile(contentsFile.value)
    
    progress.value = 60
    progressText.value = '正在合并数据...'
    
    // 合并数据
    const mergedData = mergeContentWithComments(contents, comments)
    
    // 统计信息
    let totalComments = 0
    let totalReplies = 0
    
    mergedData.forEach(content => {
      totalComments += content.comments?.length || 0
      content.comments?.forEach(comment => {
        totalReplies += comment.children?.length || 0
      })
    })
    
    progress.value = 80
    progressText.value = '正在生成Excel文件...'
    
    // 动态导入 XLSX 库
    const XLSX = await import('xlsx')
    
    // 生成Excel文件
    const worksheet = createExcelWorkbook(mergedData)
    const workbook = XLSX.utils.book_new()
    const sheetNames = {
      xiaohongshu: '小红书数据分析',
      zhihu: '知乎数据分析',
      bilibili: 'B站数据分析'
    }
    XLSX.utils.book_append_sheet(workbook, XLSX.utils.aoa_to_sheet(worksheet), sheetNames[selectedPlatform.value])
    
    // 生成文件名
    const now = new Date()
    const dateStr = now.toISOString().split('T')[0]
    const platformNames = {
      xiaohongshu: '小红书',
      zhihu: '知乎',
      bilibili: 'B站'
    }
    const fileName = `${platformNames[selectedPlatform.value]}数据分析_${dateStr}.xlsx`
    
    // 自动下载文件
    XLSX.writeFile(workbook, fileName)
    
    progress.value = 100
    progressStatus.value = 'success'
    progressText.value = '处理完成！'
    
    result.value = {
      success: true,
      message: `Excel文件已生成并自动下载: ${fileName}`,
      stats: {
        originalContents: contents.length,
        uniqueContents: mergedData.length,
        totalComments,
        totalReplies
      }
    }
    
    ElMessage.success('文件处理成功！')
    
  } catch (error) {
    console.error('处理失败:', error)
    progressStatus.value = 'exception'
    progressText.value = '处理失败'
    
    result.value = {
      success: false,
      message: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`
    }
    
    ElMessage.error('处理失败，请检查文件格式')
  } finally {
    processing.value = false
    setTimeout(() => {
      showProgress.value = false
    }, 3000)
  }
}
</script>

<style scoped>
.data-merge-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.upload-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.platform-section {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.platform-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 15px;
  font-size: 16px;
}

.platform-options {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.upload-section {
  margin: 20px 0;
}

.upload-item {
  margin-bottom: 30px;
}

.upload-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 10px;
}

.upload-area {
  width: 100%;
}

.action-section {
  text-align: center;
  margin: 30px 0;
}

.progress-section {
  margin: 20px 0;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.result-section {
  margin-top: 20px;
}

.stats-info {
  margin-top: 15px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.stats-info h4 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.stats-info ul {
  margin: 0;
  padding-left: 20px;
}

.stats-info li {
  margin: 5px 0;
  color: #606266;
}

:deep(.el-upload-dragger) {
  width: 100%;
}

:deep(.el-upload-list) {
  margin-top: 10px;
}

.selected-file {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin-top: 10px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  font-size: 14px;
  color: #0369a1;
}

.selected-file .remove-file {
  margin-left: auto;
  cursor: pointer;
  color: #64748b;
  transition: color 0.2s;
}

.selected-file .remove-file:hover {
  color: #ef4444;
}

.platform-hint {
  margin-top: 15px;
}
</style>