# 状态管理设计

**文档版本**: v1.0  
**所属模块**: AI富文本编辑器架构  
**最后更新**: 2025-08-22

## 3. 状态管理设计

### 3.1 状态分层架构

```typescript
// 全局状态存储
import { defineStore } from 'pinia' // 考虑集成

// 编辑器状态
export const useEditorStore = defineStore('editor', () => {
  const content = ref('')
  const selection = ref<Selection | null>(null)
  const history = ref<HistoryItem[]>([])
  const currentDocument = ref<Document | null>(null)
  
  const wordCount = computed(() => content.value.length)
  const hasSelection = computed(() => selection.value && !selection.value.isCollapsed)
  
  return {
    content,
    selection,
    history,
    currentDocument,
    wordCount,
    hasSelection
  }
})

// AI状态
export const useAIStore = defineStore('ai', () => {
  const currentModel = ref('gpt-3.5-turbo')
  const availableModels = ref<AIModel[]>([])
  const isProcessing = ref(false)
  const lastResponse = ref('')
  const requestHistory = ref<AIRequest[]>([])
  
  const processingTime = ref(0)
  const errorCount = ref(0)
  
  return {
    currentModel,
    availableModels,
    isProcessing,
    lastResponse,
    requestHistory,
    processingTime,
    errorCount
  }
})

// 文件状态
export const useFileStore = defineStore('file', () => {
  const currentFile = ref<File | null>(null)
  const fileContent = ref<string>('')
  const uploadProgress = ref(0)
  const processingStatus = ref<ProcessingStatus>('idle')
  
  const isUploading = computed(() => processingStatus.value === 'uploading')
  const isProcessing = computed(() => processingStatus.value === 'processing')
  
  return {
    currentFile,
    fileContent,
    uploadProgress,
    processingStatus,
    isUploading,
    isProcessing
  }
})
```

### 3.2 组合式函数设计

#### 3.2.1 编辑器组合式函数 (useEditor.ts)

```typescript
import { ref, computed, nextTick } from 'vue'
import type { IDomEditor } from '@wangeditor/editor'
import { useEditorStore } from '../stores/editor'

export function useEditor() {
  const store = useEditorStore()
  const editor = ref<IDomEditor | null>(null)
  
  const wordCount = computed(() => {
    if (!editor.value) return 0
    return editor.value.getText().length
  })
  
  const selectionText = computed(() => {
    if (!editor.value || !editor.value.isFocused()) return ''
    return editor.value.getSelectionText()
  })
  
  const initEditor = (container: HTMLElement) => {
    editor.value = createEditor({
      selector: container,
      config: {
        placeholder: '开始撰写您的文档...',
        readOnly: false,
        autoFocus: true,
        scroll: true,
        maxLength: 50000,
        onChange: (editor: IDomEditor) => {
          store.content = editor.getHtml()
          store.wordCount = editor.getText().length
        },
        onSelectionChange: (editor: IDomEditor) => {
          store.selection = editor.getSelection()
        }
      },
      mode: 'default',
      toolbarConfig: {
        excludeKeys: ['fullScreen'],
        insertKeys: {
          index: 0,
          keys: ['uploadWord', 'aiAssist']
        }
      },
      editorConfig: {
        MENU_CONF: {
          uploadWord: {
            async customUpload(file: File, insertFn: Function) {
              await handleWordUpload(file, insertFn)
            }
          },
          aiAssist: {
            onClick: () => {
              handleAISelection()
            }
          }
        }
      }
    })
  }
  
  const updateContent = (content: string) => {
    if (editor.value) {
      editor.value.setHtml(content)
    }
  }
  
  const insertContent = (content: string) => {
    if (editor.value) {
      editor.value.insertText(content)
    }
  }
  
  const replaceSelection = (content: string) => {
    if (editor.value) {
      editor.value.deleteFragment()
      editor.value.insertText(content)
    }
  }
  
  return {
    editor,
    wordCount,
    selectionText,
    initEditor,
    updateContent,
    insertContent,
    replaceSelection
  }
}
```

#### 3.2.2 AI组合式函数 (useAI.ts)

```typescript
import { ref, computed } from 'vue'
import { useAIStore } from '../stores/ai'
import { AIProvider } from '../services/ai.service'

export function useAI() {
  const store = useAIStore()
  const provider = new AIProvider()
  
  const availableModels = computed(() => store.availableModels)
  const currentModel = computed(() => store.currentModel)
  const isProcessing = computed(() => store.isProcessing)
  
  const loadModels = async () => {
    try {
      const models = await provider.getModels()
      store.availableModels = models
    } catch (error) {
      console.error('加载AI模型失败:', error)
    }
  }
  
  const processText = async (
    text: string, 
    model: string, 
    prompt: string,
    options?: ProcessingOptions
  ) => {
    store.isProcessing = true
    const startTime = Date.now()
    
    try {
      const result = await provider.processText({
        text,
        model,
        prompt,
        maxTokens: options?.maxTokens || 1000,
        temperature: options?.temperature || 0.7
      })
      
      store.processingTime = Date.now() - startTime
      store.lastResponse = result
      
      store.requestHistory.push({
        text,
        prompt,
        model,
        result,
        processingTime: store.processingTime,
        timestamp: Date.now()
      })
      
      return result
    } catch (error) {
      store.errorCount++
      throw error
    } finally {
      store.isProcessing = false
    }
  }
  
  return {
    availableModels,
    currentModel,
    isProcessing,
    loadModels,
    processText
  }
}
```