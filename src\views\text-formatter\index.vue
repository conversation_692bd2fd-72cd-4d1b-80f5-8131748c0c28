<template>
  <div class="text-formatter-page">
    <header class="page-header">
      <h1>文本格式化工具</h1>
      <p class="description">
        清理HTML、Markdown、Word格式，保留段落结构，标准化文本格式
      </p>
    </header>

    <div class="tool-container">
      <!-- 控制面板 -->
      <div class="control-panel">
        <div class="control-group">
          <label class="control-label">首行缩进：</label>
          <div class="control-options">
            <button 
              class="option-btn"
              :class="{ active: !indentFirstLine }"
              @click="indentFirstLine = false"
            >
              无缩进
            </button>
            <button 
              class="option-btn"
              :class="{ active: indentFirstLine && indentSize === 2 }"
              @click="setIndent(2)"
            >
              2空格
            </button>
            <button 
              class="option-btn"
              :class="{ active: indentFirstLine && indentSize === 4 }"
              @click="setIndent(4)"
            >
              4空格
            </button>
          </div>
        </div>
        <div class="control-group">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              v-model="removeMarkdown"
            >
            清理Markdown标记
          </label>
        </div>

        <div class="control-group">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              v-model="removeEmptyLines"
            >
            移除空行
          </label>
        </div>

        <div class="action-buttons">
          <button class="btn btn-secondary" @click="copyResult"
                  :disabled="!formattedText">
            复制结果
          </button>
          <button class="btn btn-tertiary" @click="clearAll"
                  :disabled="!originalText && !formattedText">
            清空
          </button>
        </div>
      </div>
      <!-- 主工作区 -->
      <div class="workspace">
        <div class="input-section">
          <label class="section-title">原始文本</label>
          <textarea 
            v-model="originalText"
            class="text-input"
            placeholder="在此粘贴需要格式化的文本...支持HTML、Markdown、Word格式"
            @input="autoFormat"
          ></textarea>
          <div class="stats" v-if="originalText">
            字符: {{ originalStats.characters }} | 
            单词: {{ originalStats.words }} | 
            段落: {{ originalStats.paragraphs }}
          </div>
        </div>

        <div class="output-section">
          <label class="section-title">格式化结果</label>
          <textarea 
            v-model="formattedText"
            class="text-output"
            readonly
            placeholder="格式化后的文本将显示在这里"
          ></textarea>
          <div class="stats" v-if="formattedText">
            字符: {{ formattedStats.characters }} | 
            单词: {{ formattedStats.words }} | 
            段落: {{ formattedStats.paragraphs }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { cleanText } from '../../utils/textCleaner'
import { formatText as formatTextUtil, getTextStats } from '../../utils/textFormatter'
import { copyToClipboard, showCopyFeedback } from '../../utils/clipboard'

// 状态管理
const originalText = ref('')
const formattedText = ref('')

const indentFirstLine = ref(false)
const indentSize = ref(2)
const removeEmptyLines = ref(false)
const removeMarkdown = ref(true)

// 计算属性
const originalStats = computed(() => getTextStats(originalText.value))
const formattedStats = computed(() => getTextStats(formattedText.value))

// 方法
const formatTextContent = () => {
  if (!originalText.value) {
    formattedText.value = ''
    return
  }

  const cleaned = cleanText(originalText.value, {
    removeHtmlTags: true,
    removeStyles: true,
    normalizeWhitespace: true,
    preserveParagraphs: true,
    removeMarkdown: removeMarkdown.value,
    removeEmptyLines: removeEmptyLines.value
  })
  
  if (cleaned.length === 0) {
    formattedText.value = ''
    return
  }
  
  const formatOptions = {
    indentFirstLine: indentFirstLine.value,
    indentSize: indentSize.value,
    removeEmptyLines: removeEmptyLines.value
  }
  

  
  const formatted = formatTextUtil(cleaned, formatOptions)
  
  formattedText.value = formatted
}

const autoFormat = () => {
  setTimeout(formatTextContent, 300)
}

const setIndent = (size: number) => {
  indentFirstLine.value = true
  indentSize.value = size
  formatTextContent()
}


const copyResult = async () => {
  if (!formattedText.value) return
  
  const success = await copyToClipboard(formattedText.value)
  if (success) {
    showCopyFeedback('格式化文本已复制到剪贴板')
  } else {
    showCopyFeedback('复制失败，请手动复制')
  }
}

const clearAll = () => {
  originalText.value = ''
  formattedText.value = ''
}

// 监听变化，实时更新
watch([indentFirstLine, indentSize, removeEmptyLines, removeMarkdown], formatTextContent)

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  const items = event.clipboardData?.items
  if (!items) return

  for (const item of items) {
    if (item.type === 'text/plain') {
      item.getAsString(text => {
        originalText.value = text
        formatTextContent()
      })
    }
  }
}

// 添加粘贴事件监听
onMounted(() => {
  window.addEventListener('paste', handlePaste as EventListener)
})

onUnmounted(() => {
  window.removeEventListener('paste', handlePaste as EventListener)
})
</script>

<style scoped>
.text-formatter-page {
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100%;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  color: #2563eb;
  margin-bottom: 0.5rem;
}

.description {
  color: #6b7280;
  font-size: 1.1rem;
}

.tool-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.control-panel {
  background: #f8fafc;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  align-items: center;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-label {
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
}

.control-options {
  display: flex;
  gap: 0.25rem;
}

.option-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  color: #000;
}

.option-btn:hover {
  border-color: #2563eb;
}

.option-btn.active {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.select-input {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  margin-left: auto;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #2563eb;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #1d4ed8;
}

.btn-secondary {
  background: #059669;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #047857;
}

.btn-tertiary {
  background: #6b7280;
  color: white;
}

.btn-tertiary:hover:not(:disabled) {
  background: #4b5563;
}

.workspace {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  padding: 1.5rem;
  min-height: 500px;
}

.input-section,
.output-section {
  display: flex;
  flex-direction: column;
}

.section-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}



.text-input,
.text-output {
  flex: 1;
  min-height: 300px;
  padding: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
}

.text-input {
  background-color: #1f2937;
  color: #fff;
}

.text-output {
  background: #f9fafb;
  color: #1f2937;
}

.text-input:focus,
.text-output:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.text-output {
  background: #f9fafb;
  color: #1f2937;
  cursor: default;
}

.stats {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

@media (max-width: 768px) {
  .text-formatter-page {
    padding: 0 1rem;
  }
  
  .control-panel {
    flex-direction: column;
    align-items: stretch;
  }
  
  .workspace {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .action-buttons {
    margin-left: 0;
    justify-content: center;
  }
}
</style>