import { ref, onMounted, onUnmounted } from 'vue'

/**
 * 文本选择工具组合式函数
 */
export function useTextSelection() {
  const selectedText = ref('')
  const selectedRange = ref<Range | null>(null)
  const toolbarPosition = ref({ x: 0, y: 0 })
  const isSelecting = ref(false)

  /**
   * 更新选择状态
   */
  const updateSelection = () => {
    const selection = window.getSelection()
    
    if (selection && selection.toString().trim().length > 0) {
      selectedText.value = selection.toString()
      selectedRange.value = selection.getRangeAt(0)
      
      // 计算工具栏位置
      const range = selection.getRangeAt(0)
      const rect = range.getBoundingClientRect()
      
      toolbarPosition.value = {
        x: rect.left + rect.width / 2,
        y: rect.top - 10 // 工具栏显示在选择文本上方
      }
      
      isSelecting.value = true
    } else {
      clearSelection()
    }
  }

  /**
   * 清除选择
   */
  const clearSelection = () => {
    selectedText.value = ''
    selectedRange.value = null
    isSelecting.value = false
  }

  /**
   * 获取选中文本的上下文
   */
  const getSelectionContext = () => {
    if (!selectedRange.value) return null
    
    const range = selectedRange.value
    const container = range.commonAncestorContainer
    const element = container.nodeType === Node.TEXT_NODE ? container.parentElement : container
    
    return {
      text: selectedText.value,
      element,
      range: selectedRange.value
    }
  }

  /**
   * 替换选中文本
   */
  const replaceSelection = (newText: string) => {
    if (!selectedRange.value) return false
    
    try {
      selectedRange.value.deleteContents()
      selectedRange.value.insertNode(document.createTextNode(newText))
      clearSelection()
      return true
    } catch (error) {
      console.error('替换选中文本失败:', error)
      return false
    }
  }

  // 监听选择变化
  const handleSelectionChange = () => {
    // 防抖处理
    setTimeout(updateSelection, 50)
  }

  onMounted(() => {
    document.addEventListener('selectionchange', handleSelectionChange)
    document.addEventListener('mouseup', handleSelectionChange)
  })

  onUnmounted(() => {
    document.removeEventListener('selectionchange', handleSelectionChange)
    document.removeEventListener('mouseup', handleSelectionChange)
  })

  return {
    selectedText,
    selectedRange,
    toolbarPosition,
    isSelecting,
    clearSelection,
    getSelectionContext,
    replaceSelection
  }
}

/**
 * 计算工具栏位置，确保不超出视窗
 */
export function calculateToolbarPosition(
  rect: DOMRect,
  toolbarWidth: number = 200,
  toolbarHeight: number = 40
) {
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  let x = rect.left + rect.width / 2 - toolbarWidth / 2
  let y = rect.top - toolbarHeight - 10

  // 边界检查
  if (x < 0) x = 10
  if (x + toolbarWidth > viewport.width) x = viewport.width - toolbarWidth - 10
  if (y < 0) y = rect.bottom + 10

  return { x, y }
}

/**
 * 检查是否支持Selection API
 */
