# AI富文本编辑器技术架构文档

**文档版本**: v1.0  
**文档类型**: 技术架构设计文档  
**项目名称**: utils-xjk企业内部工具集 - AI富文本编辑器模块  
**创建日期**: 2025-08-22  
**最后更新**: 2025-08-22  
**文档状态**: 已评审  
**技术负责人**: 开发团队  

---

## 📋 文档摘要

本文档为基于utils-xjk现有Vue3+TypeScript项目开发的AI富文本编辑器模块的完整技术架构方案。该架构在现有brownfield环境中无缝集成AI智能写作功能，确保企业内网部署的安全性和性能要求。

**核心价值**: 通过现代化的技术架构设计，在现有技术栈基础上构建高性能、可扩展的AI富文本编辑系统。

---

## 1. 架构概览

### 1.1 架构原则

| 原则类别 | 具体原则 | 实施策略 |
|----------|----------|----------|
| **兼容性** | 100%兼容现有技术栈 | 使用已集成的Vue3+TypeScript+Vite |
| **扩展性** | 模块化可插拔架构 | 组件化设计，支持功能扩展 |
| **安全性** | 基础安全保障 | 内网部署，基础数据保护 |
| **性能** | 桌面端优化体验 | 针对桌面浏览器性能优化 |
| **可维护** | 清晰的代码分层 | 严格的分层架构和开发规范 |

### 1.2 系统架构图

```mermaid
graph TB
    subgraph "表示层 (Presentation Layer)"
        UI[Vue 3 Components]
        Editor[WangEditor v5]
        AI[AI Assistant UI]
        FileUI[File Management UI]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        EditorService[Editor Core Service]
        AIService[AI Integration Service]
        FileService[File Processing Service]
        CacheService[Cache Management]
    end
    
    subgraph "数据访问层 (Data Access Layer)"
        API[api.agicto.cn/v1 Client]
        Parser[Word Parser]
        Storage[Local Storage Manager]
        Security[Security Manager]
    end
    
    subgraph "基础设施层 (Infrastructure Layer)"
        HTTP[HTTP Client]
        Crypto[Cryptography]
        Logger[Logging System]
        Monitor[Performance Monitor]
    end
    
    UI --> EditorService
    Editor --> EditorService
    AI --> AIService
    FileUI --> FileService
    
    EditorService --> AIService
    EditorService --> FileService
    EditorService --> CacheService
    
    AIService --> API
    FileService --> Parser
    CacheService --> Storage
    Security --> Crypto
    
    API --> HTTP
    Storage --> Security
    Monitor --> Logger
```

### 1.3 技术栈矩阵

| 层级 | 技术组件 | 版本 | 选择理由 |
|------|----------|------|----------|
| **前端框架** | Vue.js | 3.5.18 | 已集成，响应式数据流 |
| **类型系统** | TypeScript | 5.8.3 | 严格类型检查，减少错误 |
| **构建工具** | Vite | 7.1.0 | 快速构建，开发体验好 |
| **UI框架** | Element Plus | 2.10.7 | 企业级组件，已集成 |
| **富文本** | WangEditor v5 | 5.1.23 | 功能完善，易于定制 |
| **Word解析** | Mammoth.js | 1.10.0 | DOCX格式解析，轻量级 |
| **文件处理** | JSZip | 3.10.1 | ZIP文件处理，支持DOCX |
| **状态管理** | Pinia | ^2.1.7 | 考虑集成，Vue官方推荐 |

---

## 2. 前端组件架构

### 2.1 组件层次结构

```
src/views/ai-rich-editor/
├── components/                  # 业务组件
│   ├── RichEditor/
│   │   ├── index.vue           # 主编辑器组件
│   │   ├── EditorToolbar.vue   # 工具栏组件
│   │   ├── EditorContent.vue   # 编辑区域组件
│   │   └── EditorStatus.vue    # 状态栏组件
│   ├── AIAssistant/
│   │   ├── index.vue           # AI助手主组件
│   │   ├── ModelSelector.vue   # 模型选择器
│   │   ├── PromptInput.vue     # 指令输入组件
│   │   └── ResponseViewer.vue  # 响应预览组件
│   ├── FileManager/
│   │   ├── index.vue           # 文件管理主组件
│   │   ├── UploadZone.vue      # 上传区域
│   │   ├── FileList.vue        # 文件列表
│   │   └── PreviewModal.vue    # 预览模态框
│   └── Common/
│       ├── LoadingSpinner.vue  # 加载动画
│       ├── ErrorBoundary.vue   # 错误边界
│       └── Notification.vue    # 通知组件
├── services/                   # 业务服务
│   ├── editor.service.ts       # 编辑器核心服务
│   ├── ai.service.ts          # AI集成服务
│   ├── file.service.ts        # 文件处理服务
│   └── storage.service.ts     # 存储服务
├── composables/               # 组合式函数
│   ├── useEditor.ts           # 编辑器逻辑
│   ├── useAI.ts              # AI功能逻辑
│   ├── useFile.ts            # 文件操作逻辑
│   └── useSecurity.ts        # 安全相关逻辑
├── types/                     # 类型定义
│   ├── editor.types.ts        # 编辑器类型
│   ├── ai.types.ts           # AI相关类型
│   └── file.types.ts         # 文件相关类型
└── utils/                     # 工具函数
    ├── editor.utils.ts        # 编辑器工具
    ├── ai.utils.ts           # AI工具函数
    └── validation.utils.ts   # 验证工具
```

### 2.2 核心组件设计

#### 2.2.1 主编辑器组件 (RichEditor/index.vue)

```typescript
<template>
  <div class="rich-editor-container">
    <EditorToolbar 
      :editor="editor"
      @command="handleCommand"
    />
    <div class="editor-main">
      <EditorContent 
        :content="content"
        @update:content="updateContent"
        @selection-change="handleSelectionChange"
      />
      <AIAssistant 
        v-if="showAI"
        :selection="currentSelection"
        :models="availableModels"
        @ai-command="handleAICommand"
      />
    </div>
    <EditorStatus 
      :status="editorStatus"
      :word-count="wordCount"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useEditor } from '../composables/useEditor'
import { useAI } from '../composables/useAI'
import { useFile } from '../composables/useFile'

// 组合式状态管理
const { 
  editor, 
  content, 
  currentSelection, 
  wordCount, 
  editorStatus,
  initEditor,
  updateContent,
  handleSelectionChange 
} = useEditor()

const { 
  showAI, 
  availableModels, 
  handleAICommand 
} = useAI()

const { handleFileOperation } = useFile()

// 命令处理
const handleCommand = (command: string, ...args: any[]) => {
  switch (command) {
    case 'upload-word':
      handleFileOperation('upload', 'word')
      break
    case 'ai-assist':
      showAI.value = true
      break
    default:
      editor.value?.execCommand(command, ...args)
  }
}

// 生命周期
onMounted(() => {
  initEditor()
})

onUnmounted(() => {
  editor.value?.destroy()
})
</script>
```

#### 2.2.2 AI助手组件 (AIAssistant/index.vue)

```typescript
<template>
  <div class="ai-assistant" v-if="isVisible">
    <ModelSelector 
      v-model="selectedModel"
      :models="models"
      @change="handleModelChange"
    />
    <PromptInput 
      v-model="prompt"
      :placeholder="promptPlaceholder"
      @submit="handleSubmit"
    />
    <ResponseViewer 
      v-if="response"
      :response="response"
      :loading="isLoading"
      @accept="handleAccept"
      @reject="handleReject"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useAI } from '../composables/useAI'

const props = defineProps<{
  selection: Selection | null
  models: AIModel[]
}>()

const emit = defineEmits<{
  aiCommand: [command: string, data: any]
}>()

const { 
  selectedModel, 
  prompt, 
  response, 
  isLoading,
  processText,
  cancelRequest 
} = useAI()

const promptPlaceholder = computed(() => {
  const length = props.selection?.toString().length || 0
  return length > 0 
    ? `对选中的${length}个字符进行优化...`
    : '请输入AI编辑指令...'
})

const handleSubmit = async () => {
  if (!props.selection || !prompt.value.trim()) return
  
  try {
    const result = await processText(
      props.selection.toString(),
      selectedModel.value,
      prompt.value
    )
    response.value = result
  } catch (error) {
    console.error('AI处理失败:', error)
  }
}

const handleAccept = () => {
  emit('aiCommand', 'replace-selection', response.value)
  reset()
}

const handleReject = () => {
  reset()
}

const reset = () => {
  prompt.value = ''
  response.value = ''
  isLoading.value = false
}
</script>
```

---

## 3. 状态管理设计

### 3.1 状态分层架构

```typescript
// 全局状态存储
import { defineStore } from 'pinia' // 考虑集成

// 编辑器状态
export const useEditorStore = defineStore('editor', () => {
  const content = ref('')
  const selection = ref<Selection | null>(null)
  const history = ref<HistoryItem[]>([])
  const currentDocument = ref<Document | null>(null)
  
  const wordCount = computed(() => content.value.length)
  const hasSelection = computed(() => selection.value && !selection.value.isCollapsed)
  
  return {
    content,
    selection,
    history,
    currentDocument,
    wordCount,
    hasSelection
  }
})

// AI状态
export const useAIStore = defineStore('ai', () => {
  const currentModel = ref('gpt-3.5-turbo')
  const availableModels = ref<AIModel[]>([])
  const isProcessing = ref(false)
  const lastResponse = ref('')
  const requestHistory = ref<AIRequest[]>([])
  
  const processingTime = ref(0)
  const errorCount = ref(0)
  
  return {
    currentModel,
    availableModels,
    isProcessing,
    lastResponse,
    requestHistory,
    processingTime,
    errorCount
  }
})

// 文件状态
export const useFileStore = defineStore('file', () => {
  const currentFile = ref<File | null>(null)
  const fileContent = ref<string>('')
  const uploadProgress = ref(0)
  const processingStatus = ref<ProcessingStatus>('idle')
  
  const isUploading = computed(() => processingStatus.value === 'uploading')
  const isProcessing = computed(() => processingStatus.value === 'processing')
  
  return {
    currentFile,
    fileContent,
    uploadProgress,
    processingStatus,
    isUploading,
    isProcessing
  }
})
```

### 3.2 组合式函数设计

#### 3.2.1 编辑器组合式函数 (useEditor.ts)

```typescript
import { ref, computed, nextTick } from 'vue'
import type { IDomEditor } from '@wangeditor/editor'
import { useEditorStore } from '../stores/editor'

export function useEditor() {
  const store = useEditorStore()
  const editor = ref<IDomEditor | null>(null)
  
  const wordCount = computed(() => {
    if (!editor.value) return 0
    return editor.value.getText().length
  })
  
  const selectionText = computed(() => {
    if (!editor.value || !editor.value.isFocused()) return ''
    return editor.value.getSelectionText()
  })
  
  const initEditor = (container: HTMLElement) => {
    editor.value = createEditor({
      selector: container,
      config: {
        placeholder: '开始撰写您的文档...',
        readOnly: false,
        autoFocus: true,
        scroll: true,
        maxLength: 50000,
        onChange: (editor: IDomEditor) => {
          store.content = editor.getHtml()
          store.wordCount = editor.getText().length
        },
        onSelectionChange: (editor: IDomEditor) => {
          store.selection = editor.getSelection()
        }
      },
      mode: 'default',
      toolbarConfig: {
        excludeKeys: ['fullScreen'],
        insertKeys: {
          index: 0,
          keys: ['uploadWord', 'aiAssist']
        }
      },
      editorConfig: {
        MENU_CONF: {
          uploadWord: {
            async customUpload(file: File, insertFn: Function) {
              await handleWordUpload(file, insertFn)
            }
          },
          aiAssist: {
            onClick: () => {
              handleAISelection()
            }
          }
        }
      }
    })
  }
  
  const updateContent = (content: string) => {
    if (editor.value) {
      editor.value.setHtml(content)
    }
  }
  
  const insertContent = (content: string) => {
    if (editor.value) {
      editor.value.insertText(content)
    }
  }
  
  const replaceSelection = (content: string) => {
    if (editor.value) {
      editor.value.deleteFragment()
      editor.value.insertText(content)
    }
  }
  
  return {
    editor,
    wordCount,
    selectionText,
    initEditor,
    updateContent,
    insertContent,
    replaceSelection
  }
}
```

#### 3.2.2 AI组合式函数 (useAI.ts)

```typescript
import { ref, computed } from 'vue'
import { useAIStore } from '../stores/ai'
import { AIProvider } from '../services/ai.service'

export function useAI() {
  const store = useAIStore()
  const provider = new AIProvider()
  
  const availableModels = computed(() => store.availableModels)
  const currentModel = computed(() => store.currentModel)
  const isProcessing = computed(() => store.isProcessing)
  
  const loadModels = async () => {
    try {
      const models = await provider.getModels()
      store.availableModels = models
    } catch (error) {
      console.error('加载AI模型失败:', error)
    }
  }
  
  const processText = async (
    text: string, 
    model: string, 
    prompt: string,
    options?: ProcessingOptions
  ) => {
    store.isProcessing = true
    const startTime = Date.now()
    
    try {
      const result = await provider.processText({
        text,
        model,
        prompt,
        maxTokens: options?.maxTokens || 1000,
        temperature: options?.temperature || 0.7
      })
      
      store.processingTime = Date.now() - startTime
      store.lastResponse = result
      
      store.requestHistory.push({
        text,
        prompt,
        model,
        result,
        processingTime: store.processingTime,
        timestamp: Date.now()
      })
      
      return result
    } catch (error) {
      store.errorCount++
      throw error
    } finally {
      store.isProcessing = false
    }
  }
  
  return {
    availableModels,
    currentModel,
    isProcessing,
    loadModels,
    processText
  }
}
```

---

## 4. API集成方案

### 4.1 API客户端架构

#### 4.1.1 AI服务封装 (ai.service.ts)

```typescript
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'

interface AIRequest {
  model: string
  messages: Array<{
    role: 'system' | 'user' | 'assistant'
    content: string
  }>
  max_tokens?: number
  temperature?: number
  stream?: boolean
}

interface AIResponse {
  id: string
  choices: Array<{
    message: {
      content: string
    }
    finish_reason: string
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

export class AIProvider {
  private client: AxiosInstance
  private baseURL = 'https://api.agicto.cn/v1'
  
  constructor() {
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${import.meta.env.VITE_AI_API_KEY}`
      }
    })
    
    this.setupInterceptors()
  }
  
  private setupInterceptors() {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 添加请求时间戳
        config.metadata = { startTime: Date.now() }
        return config
      },
      (error) => Promise.reject(error)
    )
    
    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        // 记录响应时间
        const duration = Date.now() - response.config.metadata.startTime
        console.log(`API响应时间: ${duration}ms`)
        return response
      },
      (error) => {
        // 统一错误处理
        this.handleError(error)
        return Promise.reject(error)
      }
    )
  }
  
  async processText(params: {
    text: string
    model: string
    prompt: string
    maxTokens?: number
    temperature?: number
  }): Promise<string> {
    const request: AIRequest = {
      model: params.model,
      messages: [
        {
          role: 'system',
          content: '你是一个专业的文档编辑助手，请根据用户要求优化文本内容，保持专业性和准确性。'
        },
        {
          role: 'user',
          content: `请优化以下文本：\n\n${params.text}\n\n要求：${params.prompt}`
        }
      ],
      max_tokens: params.maxTokens || 1000,
      temperature: params.temperature || 0.7
    }
    
    try {
      const response = await this.client.post<AIResponse>('/chat/completions', request)
      return response.data.choices[0]?.message.content || ''
    } catch (error) {
      throw new Error(`AI处理失败: ${error.message}`)
    }
  }
  
  async getModels(): Promise<AIModel[]> {
    try {
      const response = await this.client.get('/models')
      return response.data.data.map((model: any) => ({
        id: model.id,
        name: model.id,
        description: model.description || '',
        maxTokens: model.max_tokens
      }))
    } catch (error) {
      // 返回默认模型列表
      return [
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: '快速响应，适合日常写作', maxTokens: 4096 },
        { id: 'gpt-4', name: 'GPT-4', description: '高质量内容，适合专业文档', maxTokens: 8192 },
        { id: 'kimi-k2-0711-preview', name: 'Kimi K2', description: '中文优化，本土化写作', maxTokens: 128000 }
      ]
    }
  }
  
  private handleError(error: any) {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          console.error('API认证失败')
          break
        case 429:
          console.error('请求频率过高')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error(`API错误: ${error.response.status}`)
      }
    } else if (error.request) {
      console.error('网络连接失败')
    } else {
      console.error('请求配置错误')
    }
  }
}
```

### 4.2 错误处理与重试机制

```typescript
export class RetryableAIProvider extends AIProvider {
  private maxRetries = 3
  private retryDelay = 1000 // 初始延迟1秒
  
  async processTextWithRetry(params: any): Promise<string> {
    let lastError: Error
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await this.processText(params)
      } catch (error) {
        lastError = error as Error
        
        if (attempt === this.maxRetries) {
          throw lastError
        }
        
        // 指数退避
        const delay = this.retryDelay * Math.pow(2, attempt - 1)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    throw lastError!
  }
}
```

---

## 5. 文件处理架构

### 5.1 Word文档解析架构

#### 5.1.1 解析器设计 (word-parser.service.ts)

```typescript
import mammoth from 'mammoth'
import JSZip from 'jszip'

interface ParseOptions {
  includeDefaultStyleMap?: boolean
  styleMap?: string[]
  transformDocument?: (document: any) => any
}

interface ParseResult {
  html: string
  messages: Array<{
    type: 'warning' | 'error' | 'info'
    message: string
  }>
  images: Array<{
    name: string
    buffer: ArrayBuffer
    contentType: string
  }>
}

export class WordParser {
  private styleMap = [
    "p[style-name='Title'] => h1",
    "p[style-name='Subtitle'] => h2",
    "p[style-name='Heading 1'] => h1",
    "p[style-name='Heading 2'] => h2",
    "p[style-name='Heading 3'] => h3",
    "r[style-name='Strong'] => strong",
    "r[style-name='Emphasis'] => em"
  ]
  
  async parseFile(file: File, options: ParseOptions = {}): Promise<ParseResult> {
    const buffer = await file.arrayBuffer()
    
    try {
      const result = await mammoth.convertToHtml({
        buffer,
        options: {
          includeDefaultStyleMap: options.includeDefaultStyleMap !== false,
          styleMap: [...this.styleMap, ...(options.styleMap || [])],
          transformDocument: options.transformDocument
        }
      })
      
      // 处理图片
      const images = await this.extractImages(buffer)
      
      return {
        html: this.postProcessHtml(result.value),
        messages: result.messages,
        images
      }
    } catch (error) {
      throw new Error(`Word解析失败: ${error.message}`)
    }
  }
  
  private async extractImages(buffer: ArrayBuffer) {
    const zip = await JSZip.loadAsync(buffer)
    const images: Array<{name: string, buffer: ArrayBuffer, contentType: string}> = []
    
    const imageFiles = Object.keys(zip.files).filter(name => 
      /^word\/media\//.test(name) && 
      /\.(png|jpg|jpeg|gif|bmp)$/.test(name)
    )
    
    for (const imagePath of imageFiles) {
      const imageData = await zip.file(imagePath)?.async('arraybuffer')
      if (imageData) {
        images.push({
          name: imagePath.split('/').pop()!,
          buffer: imageData,
          contentType: this.getContentType(imagePath)
        })
      }
    }
    
    return images
  }
  
  private getContentType(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase()
    const typeMap = {
      png: 'image/png',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      gif: 'image/gif',
      bmp: 'image/bmp'
    }
    return typeMap[ext] || 'image/jpeg'
  }
  
  private postProcessHtml(html: string): string {
    // 清理HTML，移除不支持的标签
    return html
      .replace(/<o:p><\/o:p>/g, '') // 移除Office空段落
      .replace(/style="[^"]*mso-[^"]*"/g, '') // 移除MS Office样式
      .replace(/class="[^"]*Mso[^"]*"/g, '') // 移除MS Office类
  }
}
```

#### 5.1.2 文件上传服务 (file-upload.service.ts)

```typescript
export class FileUploadService {
  private maxFileSize = 50 * 1024 * 1024 // 50MB
  private allowedTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword'
  ]
  
  async uploadFile(file: File, onProgress?: (progress: number) => void): Promise<UploadResult> {
    this.validateFile(file)
    
    const result: UploadResult = {
      file,
      status: 'uploading',
      progress: 0,
      error: null
    }
    
    try {
      // 模拟上传进度
      if (onProgress) {
        for (let i = 0; i <= 100; i += 10) {
          await new Promise(resolve => setTimeout(resolve, 100))
          result.progress = i
          onProgress(i)
        }
      }
      
      // 解析Word文档
      const parser = new WordParser()
      const parseResult = await parser.parseFile(file)
      
      result.status = 'completed'
      result.parseResult = parseResult
      
      return result
    } catch (error) {
      result.status = 'error'
      result.error = error.message
      throw error
    }
  }
  
  private validateFile(file: File): void {
    if (!this.allowedTypes.includes(file.type)) {
      throw new Error('不支持的文件格式，请上传DOC或DOCX文件')
    }
    
    if (file.size > this.maxFileSize) {
      throw new Error(`文件大小超过限制（最大${this.maxFileSize / 1024 / 1024}MB）`)
    }
  }
}

interface UploadResult {
  file: File
  status: 'uploading' | 'completed' | 'error'
  progress: number
  error: string | null
  parseResult?: ParseResult
}
```

---


### 7.1 前端性能优化

#### 7.1.1 代码分割策略

```typescript
// 路由懒加载
const routes = [
  {
    path: '/ai-rich-editor',
    name: 'AI富文本编辑器',
    component: () => import('../views/ai-rich-editor/index.vue'),
    meta: {
      icon: 'Edit',
      showInMenu: true,
      preload: true // 预加载关键组件
    }
  }
]

// 组件懒加载
const AIAssistant = defineAsyncComponent(() => 
  import('../components/AIAssistant/index.vue')
)

const FileManager = defineAsyncComponent(() => 
  import('../components/FileManager/index.vue')
)
```

#### 7.1.2 缓存策略

```typescript
export class CacheManager {
  private static instance: CacheManager
  private cache = new Map<string, CacheEntry>()
  private maxSize = 50 // 最大缓存条目
  
  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager()
    }
    return CacheManager.instance
  }
  
  set(key: string, value: any, ttl: number = 300000) { // 默认5分钟
    if (this.cache.size >= this.maxSize) {
      this.evictOldest()
    }
    
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    })
  }
  
  get(key: string): any | null {
    const entry = this.cache.get(key)
    if (!entry) return null
    
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return entry.value
  }
  
  private evictOldest() {
    let oldestKey = null
    let oldestTime = Infinity
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }
}

interface CacheEntry {
  value: any
  timestamp: number
  ttl: number
}
```

### 7.2 运行时性能优化

#### 7.2.1 防抖与节流

```typescript
export function useDebounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

export function useThrottle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
```

#### 7.2.2 虚拟滚动优化

```typescript
export class VirtualScroller {
  private container: HTMLElement
  private itemHeight: number
  private bufferSize = 5
  
  constructor(container: HTMLElement, itemHeight: number) {
    this.container = container
    this.itemHeight = itemHeight
    this.setupScrollListener()
  }
  
  private setupScrollListener() {
    this.container.addEventListener('scroll', useThrottle(() => {
      this.updateVisibleItems()
    }, 16)) // 60fps
  }
  
  private updateVisibleItems() {
    const scrollTop = this.container.scrollTop
    const containerHeight = this.container.clientHeight
    
    const startIndex = Math.floor(scrollTop / this.itemHeight)
    const endIndex = Math.ceil((scrollTop + containerHeight) / this.itemHeight)
    
    const visibleStart = Math.max(0, startIndex - this.bufferSize)
    const visibleEnd = Math.min(this.totalItems, endIndex + this.bufferSize)
    
    this.renderItems(visibleStart, visibleEnd)
  }
  
  private renderItems(start: number, end: number) {
    // 实现虚拟滚动渲染逻辑
  }
}
```

---

## 8. 测试策略

### 8.1 测试金字塔

```
          /\
         /  \
        / E2E \      10% 端到端测试
       /________\
      /    \    \
     /   集成  \   30% 集成测试
    /___________\
   /   |     |   \
  /    | 单元  \  60% 单元测试
 /_____|_____|____\
```

### 8.2 单元测试

#### 8.2.1 编辑器服务测试

```typescript
// __tests__/services/editor.service.test.ts
import { describe, it, expect, vi } from 'vitest'
import { EditorService } from '../../src/views/ai-rich-editor/services/editor.service'

describe('EditorService', () => {
  let service: EditorService
  
  beforeEach(() => {
    service = new EditorService()
  })
  
  describe('content manipulation', () => {
    it('should correctly count words', () => {
      const content = '<p>Hello world</p>'
      expect(service.getWordCount(content)).toBe(2)
    })
    
    it('should handle empty content', () => {
      expect(service.getWordCount('')).toBe(0)
    })
    
    it('should handle HTML tags', () => {
      const content = '<h1>Title</h1><p>This is a test</p>'
      expect(service.getWordCount(content)).toBe(4)
    })
  })
  
  describe('selection handling', () => {
    it('should extract selected text', () => {
      const mockSelection = {
        toString: () => 'selected text'
      }
      
      const result = service.getSelectedText(mockSelection as Selection)
      expect(result).toBe('selected text')
    })
  })
})
```

#### 8.2.2 AI服务测试

```typescript
// __tests__/services/ai.service.test.ts
import { describe, it, expect, vi } from 'vitest'
import { AIProvider } from '../../src/views/ai-rich-editor/services/ai.service'

describe('AIProvider', () => {
  let provider: AIProvider
  
  beforeEach(() => {
    provider = new AIProvider()
    vi.clearAllMocks()
  })
  
  describe('processText', () => {
    it('should process text successfully', async () => {
      const mockResponse = {
        data: {
          choices: [{ message: { content: 'optimized text' } }]
        }
      }
      
      vi.spyOn(provider.client, 'post').mockResolvedValue(mockResponse)
      
      const result = await provider.processText({
        text: 'test text',
        model: 'gpt-3.5-turbo',
        prompt: 'make it better'
      })
      
      expect(result).toBe('optimized text')
    })
    
    it('should handle API errors', async () => {
      const mockError = new Error('API Error')
      vi.spyOn(provider.client, 'post').mockRejectedValue(mockError)
      
      await expect(provider.processText({
        text: 'test',
        model: 'gpt-3.5-turbo',
        prompt: 'test'
      })).rejects.toThrow('AI处理失败: API Error')
    })
  })
})
```

### 8.3 集成测试

#### 8.3.1 文件上传流程测试

```typescript
// __tests__/integration/file-upload.test.ts
import { describe, it, expect, vi } from 'vitest'
import { FileUploadService } from '../../src/views/ai-rich-editor/services/file-upload.service'
import { WordParser } from '../../src/views/ai-rich-editor/services/word-parser.service'

describe('File Upload Integration', () => {
  let uploadService: FileUploadService
  let parser: WordParser
  
  beforeEach(() => {
    uploadService = new FileUploadService()
    parser = new WordParser()
  })
  
  it('should complete full upload and parse flow', async () => {
    const mockFile = new File(['mock content'], 'test.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })
    
    // 模拟上传进度
    const progressCallback = vi.fn()
    
    const result = await uploadService.uploadFile(mockFile, progressCallback)
    
    expect(result.status).toBe('completed')
    expect(result.file).toBe(mockFile)
    expect(progressCallback).toHaveBeenCalled()
  })
})
```

### 8.4 性能测试

#### 8.4.1 编辑器性能测试

```typescript
// __tests__/performance/editor-performance.test.ts
import { describe, it, expect } from 'vitest'
import { EditorService } from '../../src/views/ai-rich-editor/services/editor.service'

describe('Editor Performance', () => {
  let service: EditorService
  
  beforeEach(() => {
    service = new EditorService()
  })
  
  it('should handle large documents efficiently', () => {
    const largeContent = '<p>'.repeat(1000) + 'test'.repeat(1000) + '</p>'.repeat(1000)
    
    const startTime = performance.now()
    const wordCount = service.getWordCount(largeContent)
    const endTime = performance.now()
    
    expect(endTime - startTime).toBeLessThan(100) // 100ms以内
    expect(wordCount).toBeGreaterThan(0)
  })
  
  it('should maintain responsive UI during AI processing', async () => {
    const mockContent = 'test content'
    
    const startTime = performance.now()
    await service.processWithAI(mockContent, 'gpt-3.5-turbo', 'optimize')
    const endTime = performance.now()
    
    expect(endTime - startTime).toBeLessThan(3000) // 3秒以内
  })
})
```

---

## 9. 部署方案

### 9.1 构建配置

#### 9.1.1 Vite生产配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@ai-editor': resolve(__dirname, 'src/views/ai-rich-editor')
    }
  },
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          'vue-vendor': ['vue', 'vue-router'],
          'ui-vendor': ['element-plus', '@element-plus/icons-vue'],
          'editor-vendor': ['@wangeditor/editor', '@wangeditor/editor-for-vue'],
          'file-vendor': ['mammoth', 'jszip', 'file-saver']
        }
      }
    }
  },
  server: {
    port: 3000,
    host: '0.0.0.0',
    headers: {
      'Cache-Control': 'no-cache'
    }
  }
})
```

### 9.2 部署检查清单

#### 9.2.1 环境变量配置

```bash
# .env.production
VITE_APP_TITLE=企业AI富文本编辑器
VITE_API_BASE_URL=/api
VITE_AI_API_KEY=your-production-api-key
VITE_MAX_FILE_SIZE=52428800
VITE_MAX_CONCURRENT_USERS=50
```

#### 9.2.2 部署脚本

```bash
#!/bin/bash
# deploy.sh

echo "开始部署AI富文本编辑器..."

# 1. 构建项目
echo "构建生产版本..."
npm run build

# 2. 运行测试
echo "运行测试套件..."
npm run test:unit
npm run test:integration

# 3. 检查构建结果
if [ -d "dist" ]; then
    echo "✅ 构建成功"
else
    echo "❌ 构建失败"
    exit 1
fi

# 4. 部署到内网服务器
echo "部署到内网服务器..."
rsync -avz --delete dist/ user@internal-server:/var/www/ai-editor/

# 5. 重启服务
ssh user@internal-server "sudo systemctl restart nginx"

echo "✅ 部署完成"
```

### 9.3 监控与维护

#### 9.3.1 健康检查端点

```typescript
// health-check.ts
export class HealthChecker {
  async checkHealth(): Promise<HealthReport> {
    const checks = await Promise.allSettled([
      this.checkAIConnection(),
      this.checkFileStorage(),
      this.checkMemoryUsage(),
      this.checkResponseTime()
    ])
    
    return {
      status: checks.every(c => c.status === 'fulfilled') ? 'healthy' : 'unhealthy',
      checks: checks.map((check, index) => ({
        name: ['AI Connection', 'File Storage', 'Memory Usage', 'Response Time'][index],
        status: check.status,
        details: check.status === 'fulfilled' ? check.value : check.reason
      })),
      timestamp: new Date().toISOString()
    }
  }
  
  private async checkAIConnection(): Promise<boolean> {
    try {
      const provider = new AIProvider()
      await provider.getModels()
      return true
    } catch {
      return false
    }
  }
  
  private checkMemoryUsage(): Promise<number> {
    return Promise.resolve(performance.memory?.usedJSHeapSize || 0)
  }
  
  private async checkResponseTime(): Promise<number> {
    const start = performance.now()
    await fetch('/api/health')
    return performance.now() - start
  }
}

interface HealthReport {
  status: 'healthy' | 'unhealthy'
  checks: Array<{
    name: string
    status: 'fulfilled' | 'rejected'
    details: any
  }>
  timestamp: string
}
```

---

## 10. 技术规范与最佳实践

### 10.1 代码规范

#### 10.1.1 TypeScript配置

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@ai-editor/*": ["src/views/ai-rich-editor/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

#### 10.1.2 ESLint配置

```typescript
// eslint.config.ts
import { defineConfig } from 'eslint-define-config'

export default defineConfig({
  extends: [
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier'
  ],
  rules: {
    // Vue相关规则
    'vue/multi-word-component-names': 'off',
    'vue/require-default-prop': 'off',
    'vue/valid-v-for': 'error',
    'vue/require-v-for-key': 'error',
    
    // TypeScript规则
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-explicit-any': 'warn',
    
    // 代码质量规则
    'no-console': ['warn', { allow: ['warn', 'error'] }],
    'prefer-const': 'error',
    'no-var': 'error'
  }
})
```

### 10.2 开发工作流

#### 10.2.1 Git工作流

```bash
# 功能开发分支命名规范
feature/ai-rich-editor-text-formatting
feature/ai-rich-editor-word-import
feature/ai-rich-editor-model-selection

# 修复分支命名规范
fix/ai-rich-editor-upload-error
fix/ai-rich-editor-ai-timeout

# 提交消息规范
git commit -m "feat(ai-editor): 添加AI文本润色功能

- 集成GPT-3.5-turbo模型
- 实现选中文本一键优化
- 添加响应时间监控

Closes #123"
```

#### 10.2.2 代码审查检查单

| 检查项目 | 要求 |
|----------|------|
| **功能完整性** | 所有功能按需求实现 |
| **类型安全** | 无TypeScript错误 |
| **代码规范** | 通过ESLint检查 |
| **测试覆盖** | 单元测试覆盖率>80% |
| **性能要求** | 关键路径性能测试通过 |
| **安全审查** | 无安全漏洞 |
| **文档更新** | 相关文档已更新 |

---

## 11. 总结

### 11.1 架构优势

1. **100%兼容现有技术栈**：基于已集成的Vue3+TypeScript+Vite，无缝集成
3. **高性能体验**：桌面端优化，代码分割，智能缓存
4. **模块化设计**：组件化架构，易于扩展和维护
5. **完整测试覆盖**：单元测试+集成测试+性能测试

### 11.2 实施建议

1. **分阶段实施**：
   - 第一阶段：基础富文本编辑器
   - 第二阶段：AI功能集成
   - 第三阶段：Word文档处理
   - 第四阶段：性能优化和测试

2. **团队培训**：
   - AI功能使用培训
   - 安全最佳实践培训
   - 性能优化技巧分享

3. **持续监控**：
   - 性能指标监控
   - 用户反馈收集
   - 定期架构评审

### 11.3 未来扩展

- **实时协作**：多人同时编辑
- **语音输入**：语音转文字
- **OCR识别**：图片文字提取
- **模板系统**：智能文档模板
- **版本控制**：文档历史版本

---

**文档结束**

*本文档为utils-xjk企业AI富文本编辑器的完整技术架构方案，所有技术决策都基于现有brownfield环境和业务需求制定。*