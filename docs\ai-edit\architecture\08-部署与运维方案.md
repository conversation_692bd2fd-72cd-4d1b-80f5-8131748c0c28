# 部署与运维方案

**文档版本**: v1.0  
**所属模块**: AI富文本编辑器架构  
**最后更新**: 2025-08-22

## 9. 部署方案

### 9.1 构建配置

#### 9.1.1 Vite生产配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@ai-editor': resolve(__dirname, 'src/views/ai-rich-editor')
    }
  },
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          'vue-vendor': ['vue', 'vue-router'],
          'ui-vendor': ['element-plus', '@element-plus/icons-vue'],
          'editor-vendor': ['@wangeditor/editor', '@wangeditor/editor-for-vue'],
          'file-vendor': ['mammoth', 'jszip', 'file-saver']
        }
      }
    }
  },
  server: {
    port: 3000,
    host: '0.0.0.0',
    headers: {
      'Cache-Control': 'no-cache'
    }
  }
})
```

### 9.2 部署检查清单

#### 9.2.1 环境变量配置

```bash
# .env.production
VITE_APP_TITLE=企业AI富文本编辑器
VITE_API_BASE_URL=/api
VITE_AI_API_KEY=your-production-api-key
VITE_MAX_FILE_SIZE=52428800
VITE_MAX_CONCURRENT_USERS=50
```

#### 9.2.2 部署脚本

```bash
#!/bin/bash
# deploy.sh

echo "开始部署AI富文本编辑器..."

# 1. 构建项目
echo "构建生产版本..."
npm run build

# 2. 运行测试
echo "运行测试套件..."
npm run test:unit
npm run test:integration

# 3. 检查构建结果
if [ -d "dist" ]; then
    echo "✅ 构建成功"
else
    echo "❌ 构建失败"
    exit 1
fi

# 4. 部署到内网服务器
echo "部署到内网服务器..."
rsync -avz --delete dist/ user@internal-server:/var/www/ai-editor/

# 5. 重启服务
ssh user@internal-server "sudo systemctl restart nginx"

echo "✅ 部署完成"
```

### 9.3 监控与维护

#### 9.3.1 健康检查端点

```typescript
// health-check.ts
export class HealthChecker {
  async checkHealth(): Promise<HealthReport> {
    const checks = await Promise.allSettled([
      this.checkAIConnection(),
      this.checkFileStorage(),
      this.checkMemoryUsage(),
      this.checkResponseTime()
    ])
    
    return {
      status: checks.every(c => c.status === 'fulfilled') ? 'healthy' : 'unhealthy',
      checks: checks.map((check, index) => ({
        name: ['AI Connection', 'File Storage', 'Memory Usage', 'Response Time'][index],
        status: check.status,
        details: check.status === 'fulfilled' ? check.value : check.reason
      })),
      timestamp: new Date().toISOString()
    }
  }
  
  private async checkAIConnection(): Promise<boolean> {
    try {
      const provider = new AIProvider()
      await provider.getModels()
      return true
    } catch {
      return false
    }
  }
  
  private checkMemoryUsage(): Promise<number> {
    return Promise.resolve(performance.memory?.usedJSHeapSize || 0)
  }
  
  private async checkResponseTime(): Promise<number> {
    const start = performance.now()
    await fetch('/api/health')
    return performance.now() - start
  }
}

interface HealthReport {
  status: 'healthy' | 'unhealthy'
  checks: Array<{
    name: string
    status: 'fulfilled' | 'rejected'
    details: any
  }>
  timestamp: string
}
```

### 9.4 内网部署配置

#### 9.4.1 Nginx配置

```nginx
# /etc/nginx/sites-available/ai-editor
server {
    listen 80;
    server_name ai-editor.company.local;
    
    # 静态资源
    location / {
        root /var/www/ai-editor;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass https://api.agicto.cn/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

#### 9.4.2 Docker部署

```dockerfile
# Dockerfile
FROM node:18-alpine as build-stage

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 9.4.3 Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  ai-editor:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:80"
    environment:
      - VITE_AI_API_KEY=${VITE_AI_API_KEY}
    restart: unless-stopped
    
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - ai-editor
    restart: unless-stopped
```

### 9.5 监控与告警

#### 9.5.1 应用监控

```typescript
// monitoring/index.ts
export class ApplicationMonitor {
  private metrics = {
    pageViews: 0,
    errors: 0,
    apiCalls: 0,
    responseTime: []
  }
  
  trackPageView(page: string) {
    this.metrics.pageViews++
    console.log(`Page view: ${page}`)
  }
  
  trackError(error: Error, context: string) {
    this.metrics.errors++
    console.error(`Error in ${context}:`, error)
    
    // 发送告警
    this.sendAlert({
      type: 'error',
      message: error.message,
      context,
      timestamp: new Date().toISOString()
    })
  }
  
  trackAPICall(duration: number, success: boolean) {
    this.metrics.apiCalls++
    this.metrics.responseTime.push(duration)
    
    if (!success) {
      this.sendAlert({
        type: 'api_error',
        duration,
        timestamp: new Date().toISOString()
      })
    }
  }
  
  private sendAlert(data: any) {
    // 发送到监控系统
    fetch('/api/alerts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    }).catch(console.error)
  }
  
  getMetrics() {
    return {
      ...this.metrics,
      avgResponseTime: this.metrics.responseTime.reduce((a, b) => a + b, 0) / this.metrics.responseTime.length
    }
  }
}
```

#### 9.5.2 日志管理

```typescript
// utils/logger.ts
export class Logger {
  private static instance: Logger
  private logs: LogEntry[] = []
  
  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }
  
  log(level: LogLevel, message: string, data?: any) {
    const entry: LogEntry = {
      level,
      message,
      data,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    }
    
    this.logs.push(entry)
    
    // 保持内存中的日志数量限制
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-500)
    }
    
    // 控制台输出
    console[level](`[${entry.timestamp}] ${message}`, data || '')
  }
  
  error(message: string, error?: Error) {
    this.log('error', message, {
      message: error?.message,
      stack: error?.stack
    })
  }
  
  warn(message: string, data?: any) {
    this.log('warn', message, data)
  }
  
  info(message: string, data?: any) {
    this.log('info', message, data)
  }
  
  exportLogs(): LogEntry[] {
    return [...this.logs]
  }
}

interface LogEntry {
  level: LogLevel
  message: string
  data?: any
  timestamp: string
  userAgent: string
}

type LogLevel = 'info' | 'warn' | 'error'
```

### 9.6 备份与恢复

#### 9.6.1 配置备份

```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/ai-editor/$DATE"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置文件
cp /var/www/ai-editor/config.json $BACKUP_DIR/
cp /etc/nginx/sites-available/ai-editor $BACKUP_DIR/nginx.conf

# 备份应用文件
tar -czf $BACKUP_DIR/app.tar.gz -C /var/www ai-editor

# 清理旧备份（保留最近7天）
find /backup/ai-editor -type d -mtime +7 -exec rm -rf {} \;

echo "备份完成: $BACKUP_DIR"
```

#### 9.6.2 恢复脚本

```bash
#!/bin/bash
# restore.sh

BACKUP_DATE=$1
BACKUP_DIR="/backup/ai-editor/$BACKUP_DATE"

if [ ! -d "$BACKUP_DIR" ]; then
    echo "备份不存在: $BACKUP_DIR"
    exit 1
fi

# 停止服务
sudo systemctl stop nginx

# 恢复应用文件
cd /var/www
sudo rm -rf ai-editor
sudo tar -xzf $BACKUP_DIR/app.tar.gz

# 恢复配置
sudo cp $BACKUP_DIR/nginx.conf /etc/nginx/sites-available/ai-editor

# 重启服务
sudo systemctl start nginx

echo "恢复完成: $BACKUP_DATE"
```

### 9.7 更新与维护策略

#### 9.7.1 蓝绿部署

```bash
#!/bin/bash
# blue-green-deploy.sh

ENV=$1
NEW_VERSION=$2

if [ "$ENV" != "blue" ] && [ "$ENV" != "green" ]; then
    echo "Usage: $0 <blue|green> <version>"
    exit 1
fi

# 部署到新环境
ssh deploy@internal-server "
  cd /var/www/ai-editor-$ENV
  git fetch origin
  git checkout $NEW_VERSION
  npm ci
  npm run build
  
  # 健康检查
  curl -f http://localhost:300$ENV/health || exit 1
  
  # 切换流量
  sudo ln -sf /etc/nginx/sites-available/ai-editor-$ENV /etc/nginx/sites-enabled/ai-editor
  sudo nginx -s reload
"

echo "已切换到 $ENV 环境: $NEW_VERSION"
```

#### 9.7.2 维护窗口

| 维护类型 | 时间安排 | 通知策略 | 回滚时间 |
|----------|----------|----------|----------|
| **紧急修复** | 立即执行 | 邮件+IM通知 | 15分钟内 |
| **计划更新** | 非工作时间 | 提前24小时通知 | 30分钟内 |
| **大版本升级** | 周末维护窗口 | 提前3天通知 | 2小时内 |

### 9.8 安全更新

#### 9.8.1 依赖检查

```bash
#!/bin/bash
# security-check.sh

# 检查npm依赖漏洞
npm audit --audit-level moderate

# 检查Docker镜像漏洞
if command -v trivy > /dev/null 2>&1; then
    trivy image nginx:alpine
fi

# 更新依赖
npm update

# 重新构建
docker build -t ai-editor:latest .
```

#### 9.8.2 SSL证书管理

```bash
#!/bin/bash
# ssl-renew.sh

# 使用Let's Encrypt自动续期
sudo certbot renew --quiet --nginx

# 检查证书有效期
openssl x509 -in /etc/ssl/certs/ai-editor.crt -text -noout | grep "Not After"

echo "SSL证书更新完成"