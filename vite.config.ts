import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  // 基础路径配置
  base: './',
  build: {
    // 代码分割优化
    rollupOptions: {
      output: {
        // 手动分包配置
        manualChunks: {
          // Vue 核心库
          'vue-core': ['vue', 'vue-router'],
          // UI 库
          'element-plus': ['element-plus', '@element-plus/icons-vue'],
          // 工具库
          'utils': ['xlsx'],
          // 公共样式
          'styles': []
        },
        // chunk 文件名格式
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId.split('/').pop()
            : 'chunk'
          return `js/${facadeModuleId}-[hash].js`
        },
        // 入口文件名格式
        entryFileNames: 'js/[name]-[hash].js',
        // 静态资源文件名格式
        assetFileNames: (assetInfo) => {
          const fileName = assetInfo.name || 'asset'
          const info = fileName.split('.')
          const ext = info[info.length - 1]
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `img/[name]-[hash].[ext]`
          }
          if (/css/i.test(ext)) {
            return `css/[name]-[hash].[ext]`
          }
          return `assets/[name]-[hash].[ext]`
        }
      }
    },
    // 设置 chunk 大小警告限制
    chunkSizeWarningLimit: 1000,
    // 启用源码映射（生产环境建议关闭）
    sourcemap: false,
    // 压缩配置
    minify: 'esbuild'
  },
  // 依赖优化选项
  optimizeDeps: {
    include: ['vue', 'vue-router', 'element-plus', '@element-plus/icons-vue', 'xlsx']
  }
})
