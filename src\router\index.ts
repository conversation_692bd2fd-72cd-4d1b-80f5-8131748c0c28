import { createRouter, createWebHashHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    redirect: '/text-formatter'
  },
  {
    path: '/text-formatter',
    name: '文本格式化',
    component: () => import('../views/text-formatter/index.vue'),
    meta: {
      icon: 'Document',
      showInMenu: true
    }
  },
  {
    path: '/data-merge-excel',
    name: '数据合并Excel',
    component: () => import('../views/data-merge-excel/index.vue'),
    meta: {
      icon: 'Magic',
      showInMenu: true
    }
  },
  {
    path: '/ai-rich-editor',
    name: 'AI富文本编辑器',
    component: () => import('../views/ai-rich-editor/index.vue'),
    meta: {
      icon: 'Edit',
      showInMenu: true
    }
  },
  // 预留更多工具页面路由
  // {
  //   path: '/tools/:toolName',
  //   name: 'ToolPage',
  //   component: () => import('../views/ToolLayout.vue')
  // }
]

const router = createRouter({
    history: createWebHashHistory(),
    routes
});

export default router