/**
 * 剪贴板操作工具
 */

/**
 * 复制文本到剪贴板
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      // 使用现代 Clipboard API
      await navigator.clipboard.writeText(text)
      return true
    } else {
      // 降级方案：使用 document.execCommand
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-9999px'
      textArea.style.top = '-9999px'
      textArea.setAttribute('readonly', '')
      
      document.body.appendChild(textArea)
      textArea.select()
      textArea.setSelectionRange(0, textArea.value.length)
      
      const success = document.execCommand('copy')
      document.body.removeChild(textArea)
      
      return success
    }
  } catch (error) {
    console.error('复制到剪贴板失败:', error)
    return false
  }
}

/**
 * 从剪贴板读取文本
 */
export async function readFromClipboard(): Promise<string | null> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      const text = await navigator.clipboard.readText()
      return text
    } else {
      // 降级方案：提示用户手动粘贴
      return null
    }
  } catch (error) {
    console.error('从剪贴板读取失败:', error)
    return null
  }
}

/**
 * 检查剪贴板权限
 */
export async function checkClipboardPermission(): Promise<boolean> {
  if (!navigator.clipboard) {
    return false
  }
  
  try {
    // 测试权限
    await navigator.clipboard.readText()
    return true
  } catch {
    return false
  }
}

/**
 * 创建复制成功的反馈
 */
export function showCopyFeedback(message = '已复制到剪贴板', duration = 2000) {
  // 创建提示元素
  const toast = document.createElement('div')
  toast.textContent = message
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    font-size: 14px;
    animation: slideIn 0.3s ease;
  `
  
  // 添加动画
  const style = document.createElement('style')
  style.textContent = `
    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
  `
  document.head.appendChild(style)
  
  document.body.appendChild(toast)
  
  setTimeout(() => {
    toast.style.animation = 'slideIn 0.3s ease reverse'
    setTimeout(() => {
      document.body.removeChild(toast)
      document.head.removeChild(style)
    }, 300)
  }, duration)
}