# 用户故事：AI富文本编辑器Word文档导入功能

## 用户故事详情

**故事编号**: US-002  
**故事标题**: Word文档导入到AI编辑器  
**优先级**: 高  
**故事点**: 5  
**迭代**: Sprint 2  
**父故事**: US-AI-001  

### 用户故事
```
作为一个 内容编辑者
我想要 将Word文档直接导入到AI富文本编辑器
以便于 利用AI功能快速优化现有文档内容
```

### 场景示例
```
场景: 商务文档AI优化
  假设 我有一份3页的商务提案Word文档
  当 我将文档拖拽到AI编辑器区域
  而且 系统成功解析文档内容
  那么 我应该看到完整格式的文档内容出现在编辑器中
  而且 我可以立即使用AI润色功能优化内容
```

## 当前项目状态分析

### 已实现功能 (US-AI-001)
✅ **已完成**:
- AI富文本编辑器基础架构
- 文本选择与AI润色功能
- 5种AI润色风格支持
- 错误处理和用户体验优化

### 待实现功能 (US-002)
🔄 **当前**: Word文档导入功能 (缺失)

## 技术实现要求

### 技术架构
- **前端框架**: Vue 3 + TypeScript + Composition API
- **文档解析**: mammoth.js (已安装 ^1.10.0)
- **UI组件**: Element Plus 2.10.7
- **状态管理**: Pinia (已实现)
- **文件处理**: 原生File API

### 功能实现

#### 1. Word文档解析服务
**文件**: `src/views/ai-rich-editor/utils/word-parser.ts`

```typescript
export class WordParser {
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024
  private readonly ALLOWED_TYPES = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword'
  ]

  async parseWordDocument(file: File): Promise<{html: string; text: string}> {
    this.validateFile(file)
    
    const arrayBuffer = await file.arrayBuffer()
    const result = await mammoth.convertToHtml({ arrayBuffer })
    
    return {
      html: result.value,
      text: result.value.replace(/<[^\u003e]*>/g, '').trim()
    }
  }

  validateFile(file: File): void {
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      throw new Error('请上传 .docx 或 .doc 格式的Word文档')
    }
    
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error('文件大小不能超过10MB')
    }
  }
}
```

#### 2. 上传组件实现
**文件**: `src/views/ai-rich-editor/components/WordImportButton.vue`

#### 3. 集成使用示例
```typescript
// 在AI编辑器主页面中集成
const handleWordImport = async (file: File) => {
  try {
    const parser = new WordParser()
    const content = await parser.parseWordDocument(file)
    editorStore.setContent(content.html, 'import', `从 ${file.name} 导入`)
    ElMessage.success('文档导入成功')
  } catch (error) {
    ElMessage.error(error.message)
  }
}
```

## 验收标准

### 功能验收
- [ ] 支持 .docx 和 .doc 格式文件上传
- [ ] 文件大小限制10MB，超出提示友好错误
- [ ] 上传过程显示进度条
- [ ] 解析后内容完整显示在编辑器中
- [ ] 支持拖拽上传
- [ ] 上传失败后支持重试

### 技术验收
- [ ] 解析过程不丢失主要格式（标题、段落、列表）
- [ ] 图片以base64格式正确嵌入
- [ ] 解析时间：1MB文档<3秒
- [ ] 内存使用：解析过程<50MB

## 开发任务

| 任务 | 描述 | 时间 |
|------|------|------|
| TASK-001 | 创建Word解析服务类 | 2小时 |
| TASK-002 | 实现文件验证逻辑 | 1小时 |
| TASK-003 | 创建上传UI组件 | 2小时 |
| TASK-004 | 集成到主页面 | 1小时 |
| TASK-005 | 添加错误处理 | 1小时 |

**总计**: 7小时

## 技术准备

### ✅ 已具备条件
- mammoth.js 依赖已安装 (^1.10.0)
- AI编辑器基础架构已完成
- Pinia状态管理已就绪
- 文件上传UI组件框架已存在

### 🚀 立即可开始开发
当前项目状态支持立即开始US-002的开发实施。开发AI可以直接基于这个文档开始编码工作。

---
**创建日期**: 2025-08-24  
**状态**: 已审查，准备开发  
**关联**: US-AI-001的后续增强功能