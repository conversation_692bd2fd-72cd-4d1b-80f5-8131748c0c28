<execution>
  <constraint>
    ## 企业内部工具开发约束
    - **时间约束**：每个工具开发周期2-5天
    - **技术约束**：基于Vue 3 + TypeScript + Element Plus
    - **用户约束**：非技术背景的企业内部员工
    - **部署约束**：内网部署，无外部依赖
    - **质量约束**：稳定性≥99%，用户满意度≥90%
  </constraint>
  
  <rule>
    ## 产品开发强制规则
    - **单一职责**：每个工具只解决一个核心问题
    - **零学习成本**：用户无需培训即可使用
    - **渐进式功能**：从MVP开始逐步完善
    - **数据安全**：所有数据处理在客户端完成
    - **快速迭代**：基于用户反馈快速调整
  </rule>
  
  <guideline>
    ## 产品开发指导原则
    - **用户故事优先**：基于真实工作场景设计功能
    - **简化交互**：最大限度减少用户操作步骤
    - **错误预防**：设计防止用户出错的机制
    - **即时反馈**：每个操作都有明确的反馈
    - **渐进披露**：复杂功能逐步展示
  </guideline>
  
  <process>
    ## 企业内部工具开发流程
    
    ```mermaid
    flowchart TD
        Start([需求发现]) --> Research[用户调研]
        Research --> Analysis[需求分析]
        Analysis --> Design[功能设计]
        Design --> Prototype[原型制作]
        Prototype --> Test[用户测试]
        Test --> Develop[开发实现]
        Develop --> Deploy[内部部署]
        Deploy --> Feedback[收集反馈]
        Feedback --> Iterate[迭代优化]
        Iterate --> End([正式发布])
        
        Research -.->|1-2天| A1[观察+访谈]
        Design -.->|1天| B1[功能规格说明]
        Test -.->|1天| C1[5人用户测试]
        Develop -.->|2-3天| D1[敏捷开发]
    ```
    
    ### 详细步骤说明
    
    #### 1. 需求发现阶段
    - **目标**：识别真实的企业内部痛点
    - **方法**：
      - 观察员工日常工作流程
      - 记录重复性任务和效率瓶颈
      - 分析现有工具的不足之处
    - **输出**：需求问题清单
    
    #### 2. 用户调研阶段
    - **目标**：深入理解目标用户特征
    - **方法**：
      - 半结构化用户访谈
      - 工作流程shadowing
      - 痛点优先级排序
    - **输出**：用户画像和痛点地图
    
    #### 3. 需求分析阶段
    - **目标**：将痛点转化为产品需求
    - **方法**：
      - 需求可行性评估
      - ROI计算和优先级排序
      - 技术方案初步评估
    - **输出**：产品需求文档(PRD)
    
    #### 4. 功能设计阶段
    - **目标**：设计最小可行产品(MVP)
    - **方法**：
      - 功能范围界定
      - 交互流程设计
      - 界面原型设计
    - **输出**：功能规格说明书
    
    #### 5. 原型制作阶段
    - **目标**：快速验证设计假设
    - **方法**：
      - 低保真原型(Figma/HTML)
      - 关键交互演示
      - 用户流程验证
    - **输出**：可交互原型
    
    #### 6. 用户测试阶段
    - **目标**：验证产品可用性
    - **方法**：
      - 5人用户测试
      - 任务完成率测试
      - 用户满意度调查
    - **输出**：测试报告和优化建议
    
    #### 7. 开发实现阶段
    - **目标**：快速构建可用版本
    - **方法**：
      - 敏捷开发(2-3天冲刺)
      - 代码审查和质量保证
      - 持续集成和部署
    - **输出**：可部署的产品版本
    
    #### 8. 内部部署阶段
    - **目标**：在小范围内试用
    - **方法**：
      - 选择5-10个种子用户
      - 提供基础使用培训
      - 建立反馈收集机制
    - **输出**：部署文档和用户反馈
    
    #### 9. 收集反馈阶段
    - **目标**：收集真实使用反馈
    - **方法**：
      - 使用数据统计分析
      - 用户访谈和问卷
      - 问题收集和分类
    - **输出**：反馈分析报告
    
    #### 10. 迭代优化阶段
    - **目标**：基于反馈持续改进
    - **方法**：
      - 快速修复关键问题
      - 功能增强和优化
      - 用户体验改进
    - **输出**：优化版本和更新日志
  </process>
  
  <criteria>
    ## 产品质量评价标准
    
    ### 功能完整性
    - ✅ 核心功能100%实现
    - ✅ 边缘场景处理完善
    - ✅ 错误提示清晰友好
    - ✅ 数据备份和恢复机制
    
    ### 用户体验
    - ✅ 首次使用成功率≥90%
    - ✅ 任务完成时间减少≥60%
    - ✅ 用户满意度≥90%
    - ✅ 学习成本趋近于零
    
    ### 技术质量
    - ✅ 代码覆盖率≥80%
    - ✅ 性能指标达标
    - ✅ 安全性无漏洞
    - ✅ 可维护性良好
    
    ### 商业价值
    - ✅ 工作效率提升≥50%
    - ✅ 重复劳动减少≥60%
    - ✅ 用户采用率≥80%
    - ✅ ROI为正
  </criteria>
</execution>