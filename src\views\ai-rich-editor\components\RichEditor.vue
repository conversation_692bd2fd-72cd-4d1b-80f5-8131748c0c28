<template>
   <div style="border: 1px solid #ccc">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="'default'"
      />
      <Editor
        style="height: 500px; overflow-y: hidden;"
        v-model="valueHtml"
        :defaultConfig="editorConfig"
        :mode="'default'"
        @onCreated="handleCreated"
      />
    </div>
</template>

<script setup lang="ts">
 import '@wangeditor/editor/dist/css/style.css' // 引入 css
 import { onBeforeUnmount, ref, shallowRef, onMounted } from 'vue'
 import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

 const editorRef = shallowRef()
 const valueHtml = ref('<p>hello</p>')

  const toolbarConfig = {
    hoverbarKeys:[],
  }
  const editorConfig = { 
    hoverbarKeys:{
      'text': {
       
        menuKeys: [
          
        ], // 定义你想要的 menu keys
    }
    },
    placeholder: '请输入内容...'
   }

      // 组件销毁时，也及时销毁编辑器
  onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor == null) return
    editor.destroy()
  })

  const handleCreated = (editor) => {
        editorRef.value = editor // 记录 editor 实例，重要！
  }

</script>

<style scoped>

</style>