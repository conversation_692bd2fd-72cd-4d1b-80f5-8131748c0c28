<template>
  <div class="rich-editor">
    <div class="toolbar-container">
      <div ref="toolbarRef" class="editor-toolbar"></div>
    </div>
    <div ref="editorRef" class="editor-content"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { createEditor, createToolbar } from '@wangeditor/editor'
import type { IDomEditor, IEditorConfig } from '@wangeditor/editor'

const props = withDefaults(defineProps<{
  modelValue?: string
  placeholder?: string
  readonly?: boolean
  height?: string | number
}>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  readonly: false,
  height: '400px'
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'selection-change', selection: string): void
  (e: 'ai-write-request', text: string): void
  (e: 'ai-polish-request', text: string): void
  (e: 'ready'): void
}>()

// 清理编辑器内容的函数
const clearEditor = () => {
  if (editor.value) {
    editor.value.setHtml('')
    console.log('编辑器内容已清理')
  }
}

const editorRef = ref<HTMLElement | null>(null)
const toolbarRef = ref<HTMLElement | null>(null)
const editor = ref<IDomEditor | null>(null)
const toolbar = ref<any>(null)

// 选择状态
const hasSelection = ref(false)
const currentSelection = ref('')

// 初始化编辑器
const initEditor = async () => {
  if (!editorRef.value || !toolbarRef.value) return

  try {

    const editorConfig: Partial<IEditorConfig> = {
      placeholder: props.placeholder,
      readOnly: props.readonly,
      autoFocus: false,
      scroll: true,
      hoverbarKeys: {
         'text': {
        menuKeys: [
          'bold',
          'italic',
          'through',
          '|',
          'color',
          'bgColor'
        ], // 定义你想要的 menu keys
    }
      },
      MENU_CONF: {
        // 配置图片上传为base64
        uploadImage: {
          // 自定义上传函数，将图片转换为base64
          customUpload: (file: File, insertFn: Function) => {
            console.log('开始处理图片:', file.name)

            // 检查文件类型
            if (!file.type.startsWith('image/')) {
              console.error('请选择图片文件')
              return
            }

            // 检查文件大小 (限制为5MB)
            const maxSize = 5 * 1024 * 1024
            if (file.size > maxSize) {
              console.error('图片大小不能超过5MB')
              return
            }

            // 使用FileReader转换为base64
            const reader = new FileReader()
            reader.onload = (e) => {
              const base64 = e.target?.result as string
              console.log('图片转换完成，插入编辑器')

              // 插入图片到编辑器
              insertFn(base64, file.name, base64)
            }
            reader.onerror = () => {
              console.error('图片读取失败')
            }
            reader.readAsDataURL(file)
          }
        }
      }
    }

    editor.value = createEditor({
      selector: editorRef.value,
      config: editorConfig,
      html: props.modelValue,
      mode: 'default'
    })

    // 自定义工具栏配置，移除视频功能
    const toolbarConfig = {
      toolbarKeys: [
        'headerSelect',
        '|',
        'bold',
        'italic',
        'underline',
        'through',
        '|',
        'color',
        'bgColor',
        '|',
        'bulletedList',
        'numberedList',
        '|',
        'justifyLeft',
        'justifyCenter',
        'justifyRight',
        '|',
        'insertLink',
        'insertImage',
        // 移除了 'insertVideo' 和 'deleteVideo'
        '|',
        'blockquote',
        'codeBlock',
        '|',
        'undo',
        'redo'
      ]
    }
    

    toolbar.value = createToolbar({
      editor: editor.value,
      selector: toolbarRef.value,
      config: toolbarConfig,
      mode: 'default'
    })

    console.log('工具栏配置:', toolbarConfig)
    console.log('编辑器实例:', editor.value)

    // 检查编辑器是否正常工作
    setTimeout(() => {
      if (editor.value) {
        console.log('编辑器已初始化完成')
        console.log('当前HTML内容:', editor.value.getHtml())

        // 添加AI功能按钮到悬浮工具栏
        addAIButtonsToHoverbar()
      }
    }, 500)

    // 监听选区变化
    editor.value.on('selectionChange', () => {
      const selText = editor.value?.getSelectionText() || ''
      console.log('WangEditor选择文本变化:', selText) // 调试信息

      // 更新选择状态
      currentSelection.value = selText
      hasSelection.value = !!selText.trim()

      emit('selection-change', selText)
    })

    // 添加额外的鼠标事件监听，确保选择被捕获
    const editorElement = editorRef.value
    if (editorElement) {
      editorElement.addEventListener('mouseup', () => {
        setTimeout(() => {
          const selText = editor.value?.getSelectionText() || ''
          console.log('鼠标抬起后选择文本:', selText) // 调试信息

          // 更新选择状态
          currentSelection.value = selText
          hasSelection.value = !!selText.trim()

          if (selText) {
            emit('selection-change', selText)
          }
        }, 100)
      })
    }

    // 监听内容变化
    editor.value.on('change', () => {
      const html = editor.value?.getHtml() || ''
      console.log('编辑器内容变化:', html) // 调试信息
      emit('update:modelValue', html)
    })

    emit('ready')
  } catch (error) {
    console.error('初始化编辑器失败:', error)
  }
}

// 生命周期钩子
onMounted(() => {
  initEditor()
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})

// 监听属性变化
watch(() => props.modelValue, (newValue) => {
  if (editor.value && newValue !== editor.value.getHtml()) {
    editor.value.setHtml(newValue)
  }
})

// AI功能处理方法
const handleAIWrite = () => {
  if (currentSelection.value.trim()) {
    emit('ai-write-request', currentSelection.value)
  }
}

const handleAIPolish = () => {
  if (currentSelection.value.trim()) {
    emit('ai-polish-request', currentSelection.value)
  }
}

// 添加AI按钮到悬浮工具栏
const addAIButtonsToHoverbar = () => {
  // 使用MutationObserver监听悬浮工具栏的出现
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement
          // 查找悬浮工具栏
          const hoverbar = element.querySelector('.w-e-bar') ||
                          (element.classList?.contains('w-e-bar') ? element : null)

          if (hoverbar && !hoverbar.querySelector('.ai-buttons-added')) {
            // 添加AI按钮
            addAIButtonsToBar(hoverbar as HTMLElement)
          }
        }
      })
    })
  })

  // 监听整个文档的变化
  observer.observe(document.body, {
    childList: true,
    subtree: true
  })

  // 组件销毁时断开观察
  onBeforeUnmount(() => {
    observer.disconnect()
  })
}

// 向工具栏添加AI按钮
const addAIButtonsToBar = (toolbar: HTMLElement) => {
  // 创建AI编写按钮
  const aiWriteBtn = document.createElement('button')
  aiWriteBtn.className = 'w-e-bar-item w-e-bar-item-button'
  aiWriteBtn.innerHTML = `
    <svg viewBox="0 0 1024 1024" style="width: 14px; height: 14px;">
      <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="currentColor"></path>
      <path d="M464 336a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z" fill="currentColor"></path>
    </svg>
  `
  aiWriteBtn.title = 'AI编写'
  aiWriteBtn.style.backgroundColor = '#67c23a'
  aiWriteBtn.style.color = 'white'
  aiWriteBtn.style.border = 'none'
  aiWriteBtn.style.borderRadius = '4px'
  aiWriteBtn.style.margin = '0 2px'
  aiWriteBtn.style.padding = '4px 8px'
  aiWriteBtn.style.cursor = 'pointer'
  aiWriteBtn.onclick = handleAIWrite

  // 创建AI润色按钮
  const aiPolishBtn = document.createElement('button')
  aiPolishBtn.className = 'w-e-bar-item w-e-bar-item-button'
  aiPolishBtn.innerHTML = `
    <svg viewBox="0 0 1024 1024" style="width: 14px; height: 14px;">
      <path d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM513.1 518.1l-192 192c-4.7 4.7-12.3 4.7-17 0l-96-96c-4.7-4.7-4.7-12.3 0-17l17-17c4.7-4.7 12.3-4.7 17 0l71 71 167-167c4.7-4.7 12.3-4.7 17 0l17 17c4.7 4.7 4.7 12.3 0 17z" fill="currentColor"></path>
    </svg>
  `
  aiPolishBtn.title = 'AI润色'
  aiPolishBtn.style.backgroundColor = '#409eff'
  aiPolishBtn.style.color = 'white'
  aiPolishBtn.style.border = 'none'
  aiPolishBtn.style.borderRadius = '4px'
  aiPolishBtn.style.margin = '0 2px'
  aiPolishBtn.style.padding = '4px 8px'
  aiPolishBtn.style.cursor = 'pointer'
  aiPolishBtn.onclick = handleAIPolish

  // 添加分隔符
  const separator = document.createElement('span')
  separator.className = 'w-e-bar-item w-e-bar-item-divider'
  separator.innerHTML = '|'
  separator.style.margin = '0 4px'
  separator.style.color = '#ccc'

  // 将按钮添加到工具栏
  toolbar.appendChild(separator)
  toolbar.appendChild(aiWriteBtn)
  toolbar.appendChild(aiPolishBtn)

  // 标记已添加，避免重复添加
  toolbar.classList.add('ai-buttons-added')
}

// 对外暴露的方法
const setContent = (html: string) => {
  if (editor.value) {
    editor.value.setHtml(html)
  }
}

const replaceSelection = (text: string) => {
  if (editor.value) {
    editor.value.insertText(text)
  }
}

defineExpose({
  setContent,
  clearEditor,
  replaceSelection
})
</script>

<style scoped>
.rich-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.toolbar-container {
  display: flex;
  align-items: center;
  background-color: #fafafa;
  border-bottom: 1px solid #dcdfe6;
}

.editor-toolbar {
  flex: 1;
}

.custom-toolbar-buttons {
  padding: 4px 8px;
  border-left: 1px solid #dcdfe6;
}

.ai-write-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
  color: #606266;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  height: 28px;
}

.ai-write-btn:hover:not(:disabled) {
  background: #ecf5ff;
  color: #409eff;
  border-color: #c6e2ff;
}

.ai-write-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f7fa;
}

.ai-icon {
  width: 14px;
  height: 14px;
}

.editor-content {
  min-height: v-bind(height);
}

:deep(.w-e-toolbar) {
  background-color: #fafafa !important;
  padding: 4px 8px !important;
}

:deep(.w-e-text-container) {
  background-color: #fff !important;
}

/* 确保列表样式正常显示 */
:deep(.w-e-text-container ul) {
  list-style-type: disc !important;
  margin: 10px 0 !important;
  padding-left: 20px !important;
}

:deep(.w-e-text-container ol) {
  list-style-type: decimal !important;
  margin: 10px 0 !important;
  padding-left: 20px !important;
}

:deep(.w-e-text-container li) {
  display: list-item !important;
  margin: 5px 0 !important;
}

/* 最小化的Word文档样式 - 避免与编辑器冲突 */
:deep(.w-e-text-container .doc-main-title) {
  font-size: 22px;
  font-weight: bold;
  text-align: center;
  margin: 20px 0 30px;
}

:deep(.w-e-text-container .doc-subtitle) {
  font-size: 16px;
  font-weight: bold;
  margin: 16px 0;
}

:deep(.w-e-text-container .doc-paragraph) {
  text-indent: 2em;
  line-height: 1.8;
  margin: 12px 0;
}
</style>