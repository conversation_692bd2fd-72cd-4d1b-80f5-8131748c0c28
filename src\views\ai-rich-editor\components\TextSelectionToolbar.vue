<template>
  <Transition name="fade">
    <div
      v-if="visible && selectedText"
      class="text-selection-toolbar"
      :style="positionStyle"
      @mousedown.stop
    >
      <div class="toolbar-content">
        <el-button-group>
          <el-button
            size="small"
            @click="handlePolishClick"
            :loading="isProcessing"
            type="primary"
          >
            <el-icon><MagicStick /></el-icon>
            AI润色
          </el-button>
          <el-button
            size="small"
            @click="handleExpand"
            :loading="isProcessing"
          >
            <el-icon><Edit /></el-icon>
            扩写
          </el-button>
          <el-button
            size="small"
            @click="handleSummarize"
            :loading="isProcessing"
          >
            <el-icon><Reading /></el-icon>
            总结
          </el-button>
          <el-button
            size="small"
            @click="handleAIWrite"
            :loading="isProcessing"
            type="success"
          >
            <el-icon><Cpu /></el-icon>
            AI编写
          </el-button>
        </el-button-group>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import type { CSSProperties } from 'vue'
import { MagicStick, Edit, Reading, Cpu } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { calculateToolbarPosition } from '../utils/selection-utils'

interface Props {
  selectedText?: string
  visible?: boolean
  isProcessing?: boolean
}

interface Emits {
  'polish-request': [text: string, position: { x: number; y: number }]
  'expand-request': [text: string]
  'summarize-request': [text: string]
  'ai-write-request': [text: string]
  'close': []
}

const props = withDefaults(defineProps<Props>(), {
  selectedText: '',
  visible: false,
  isProcessing: false
})

const emit = defineEmits<Emits>()

const position = ref({ x: 0, y: 0 })
const toolbarRef = ref<HTMLElement>()
const selected = computed(() => props.selectedText.trim().length > 0)

const positionStyle = computed((): CSSProperties => ({
  position: 'fixed',
  left: `${position.value.x}px`,
  top: `${position.value.y}px`,
  zIndex: 9999,
  transform: 'translateX(-50%)'
}))


// 工具栏样式计算
const toolbarStyle = computed(() => ({
  left: `${position.value.x}px`,
  top: `${position.value.y}px`
}))

// 处理选择变化
const updateToolbarPosition = () => {
  console.log('更新工具栏位置') // 调试信息
  const selection = window.getSelection()
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    const rect = range.getBoundingClientRect()
    console.log('选择区域位置:', rect) // 调试信息

    position.value = calculateToolbarPosition(rect, 200, 40)
    console.log('工具栏位置:', position.value) // 调试信息
  }
}

// 处理复制
const handleCopy = async () => {
  try {
    await navigator.clipboard.writeText(props.selectedText)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// AI功能处理
const handlePolishClick = () => {
  emit('polish-request', props.selectedText, position.value)
}

const handleExpand = () => {
  emit('expand-request', props.selectedText)
}

const handleSummarize = () => {
  emit('summarize-request', props.selectedText)
}

const handleAIWrite = () => {
  emit('ai-write-request', props.selectedText)
}

// 处理关闭
const handleClose = () => {
  emit('close')
  // 清除选择
  window.getSelection()?.removeAllRanges()
}

// 监听外部点击关闭
const handleOutsideClick = (event: MouseEvent) => {
  const target = event.target as Node
  if (toolbarRef.value && !toolbarRef.value.contains(target)) {
    // 检查是否点击了编辑器区域
    const editorArea = document.querySelector('.rich-editor')
    if (!editorArea || !editorArea.contains(target)) {
      handleClose()
    }
  }
}

// 监听编辑器失焦
const handleEditorBlur = () => {
  // 延迟关闭，给用户时间点击工具栏
  setTimeout(() => {
    const selection = window.getSelection()
    if (!selection || selection.toString().trim() === '') {
      handleClose()
    }
  }, 200)
}

// 监听 visible 和 selectedText 的变化
watch([() => props.visible, () => props.selectedText], ([newVisible, newText]) => {
  console.log('工具栏属性变化:', { visible: newVisible, text: newText }) // 调试信息
  if (newVisible && newText) {
    // 延迟更新位置，确保DOM已更新
    setTimeout(updateToolbarPosition, 10)
  }
})

onMounted(() => {
  document.addEventListener('selectionchange', updateToolbarPosition)
  document.addEventListener('mouseup', updateToolbarPosition)
  document.addEventListener('mousedown', handleOutsideClick)
  document.addEventListener('blur', handleEditorBlur, true)
})

onUnmounted(() => {
  document.removeEventListener('selectionchange', updateToolbarPosition)
  document.removeEventListener('mouseup', updateToolbarPosition)
  document.removeEventListener('mousedown', handleOutsideClick)
  document.removeEventListener('blur', handleEditorBlur, true)
})

// 暴露方法给父组件
const updatePosition = (newPosition: { x: number; y: number }) => {
  position.value = newPosition
}

defineExpose({
  updatePosition
})
</script>

<style scoped>
.text-selection-toolbar {
  position: fixed;
  z-index: 9999;
  transform: translateX(-50%);
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 4px;
}

.toolbar-content {
  display: flex;
  gap: 4px;
}

.el-button-group {
  display: flex;
  gap: 1px;
}

.el-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(-10px);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .text-selection-toolbar {
    transform: none;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%);
  }
}
</style>