<template>
  <el-container class="app-layout">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '240px'" class="sidebar">
      <div class="logo">
        <el-icon size="24"><Tools /></el-icon>
        <span v-show="!isCollapse">工具箱</span>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        class="menu"
        :router="true"
        :collapse="isCollapse"
      >
        <el-menu-item 
          v-for="route in menuRoutes" 
          :key="route.path"
          :index="route.path"
          :route="route.path"
        >
          <el-icon>
            <component :is="route.icon" />
          </el-icon>
          <template #title>{{ route.name }}</template>
        </el-menu-item>
      </el-menu>
      
      <!-- 折叠按钮 -->
      <div class="collapse-btn" @click="toggleCollapse">
        <el-icon size="20">
          <Expand v-if="isCollapse" />
          <Fold v-else />
        </el-icon>
      </div>
    </el-aside>
    
    <!-- 主内容区 -->
    <el-container class="main-container">
      <el-main>
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const isCollapse = ref(false)

// 当前激活的菜单项
const activeMenu = computed(() => route.path)

// 获取需要显示在菜单中的路由
const menuRoutes = computed(() => {
  return router.getRoutes()
    .filter(route => 
      route.path !== '/' && 
      route.path !== '/:pathMatch(.*)*' &&
      route.meta?.showInMenu !== false &&
      route.name
    )
    .map(route => ({
      path: route.path,
      name: route.name?.toString() || route.path,
      icon: route.meta?.icon || 'Document'
    }))
})

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style scoped>
.app-layout {
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  background: #545c64;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #dcdfe6;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #434a50;
}

.logo .el-icon {
  margin-right: 10px;
}

.menu {
  flex: 1;
  border-right: none;
  background: transparent;
}

.menu :deep(.el-menu-item) {
  color: #fff;
  border-radius: 4px;
  margin: 4px 8px;
}

.menu :deep(.el-menu-item:hover) {
  background: #434a50;
  color: #fff;
}

.menu :deep(.el-menu-item.is-active) {
  background: #1890ff;
  color: #fff;
}

.menu :deep(.el-menu-item.is-disabled) {
  color: #999;
  opacity: 0.6;
  cursor: not-allowed;
}

.collapse-btn {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
  border-top: 1px solid #434a50;
  transition: all 0.3s;
}

.collapse-btn:hover {
  background: #434a50;
}

.main-container {
  flex: 1;
  background: #f5f5f5;
}

.el-main {
  padding: 20px;
  overflow-y: auto;
}

/* 折叠时的logo样式 */
.sidebar:has(.collapse-btn:hover) .logo {
  justify-content: center;
}

.sidebar:has(.collapse-btn:hover) .logo .el-icon {
  margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1000;
    transform: translateX(0);
    transition: transform 0.3s;
  }
  
  .sidebar.collapsed {
    transform: translateX(-100%);
  }
  
  .main-container {
    margin-left: 0;
  }
}
</style>