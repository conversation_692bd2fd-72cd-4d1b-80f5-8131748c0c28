<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI富文本编辑器</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:400,500,700&display=swap">
  <link rel="stylesheet" href="design_iterations/richtext_ai_theme_1.css">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.0.0/dist/flowbite.min.js"></script>
  <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
  <style>
    body {
      font-family: var(--font-sans) !important;
      background: var(--background) !important;
      color: var(--foreground) !important;
    }
    .ai-btn {
      background: var(--primary) !important;
      color: var(--primary-foreground) !important;
      border-radius: var(--radius) !important;
      box-shadow: var(--shadow) !important;
      transition: all 0.2s cubic-bezier(.4,0,.2,1);
      opacity: 0;
      transform: translateY(8px);
      pointer-events: none;
      position: absolute;
      z-index: 10;
      padding: 0.5em 1em;
      font-weight: 500;
      font-size: 1rem;
      border: none;
      cursor: pointer;
    }
    .ai-btn.visible {
      opacity: 1;
      transform: translateY(0);
      pointer-events: auto;
    }
    .ai-btn:hover {
      transform: scale(1.08);
      box-shadow: 0 8px 24px rgba(124,58,237,0.12);
    }
    .ai-modal {
      background: var(--card) !important;
      color: var(--card-foreground) !important;
      border-radius: var(--radius) !important;
      box-shadow: var(--shadow) !important;
      border: 1px solid var(--border) !important;
      padding: 2em 1.5em;
      min-width: 320px;
      max-width: 90vw;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(1);
      opacity: 0;
      pointer-events: none;
      transition: all 0.25s cubic-bezier(.4,0,.2,1);
      z-index: 50;
    }
    .ai-modal.visible {
      opacity: 1;
      pointer-events: auto;
      transform: translate(-50%, -50%) scale(1);
    }
    .ai-modal .modal-header {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1em;
      display: flex;
      align-items: center;
      gap: 0.5em;
    }
    .ai-modal input, .ai-modal textarea {
      width: 100%;
      border: 1px solid var(--border);
      border-radius: var(--radius);
      padding: 0.75em;
      font-size: 1rem;
      margin-bottom: 1em;
      background: var(--input);
      color: var(--foreground);
      outline: none;
      transition: box-shadow 0.2s;
    }
    .ai-modal input:focus, .ai-modal textarea:focus {
      box-shadow: 0 0 0 2px var(--ring);
      border-color: var(--ring);
    }
    .ai-modal .modal-actions {
      display: flex;
      gap: 1em;
      justify-content: flex-end;
    }
    .ai-modal .btn {
      padding: 0.5em 1.2em;
      border-radius: var(--radius);
      font-weight: 500;
      font-size: 1rem;
      border: none;
      cursor: pointer;
      transition: all 0.15s;
    }
    .ai-modal .btn-primary {
      background: var(--primary);
      color: var(--primary-foreground);
    }
    .ai-modal .btn-secondary {
      background: var(--secondary);
      color: var(--secondary-foreground);
    }
    .ai-modal .btn-primary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    .ai-modal .loading-spinner {
      display: inline-block;
      width: 1.5em;
      height: 1.5em;
      border: 3px solid var(--muted);
      border-top: 3px solid var(--primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 0.5em;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    .highlighted {
      background: var(--accent) !important;
      transition: background 0.3s;
      animation: highlight-fade 2s forwards;
    }
    @keyframes highlight-fade {
      0% { background: var(--accent); }
      80% { background: var(--accent); }
      100% { background: transparent; }
    }
    .editor-toolbar {
      background: var(--card);
      border-bottom: 1px solid var(--border);
      padding: 0.75em 1.5em;
      display: flex;
      gap: 1em;
      align-items: center;
      border-radius: var(--radius) var(--radius) 0 0;
    }
    .editor-toolbar button {
      background: var(--secondary);
      color: var(--secondary-foreground);
      border: none;
      border-radius: var(--radius);
      padding: 0.5em 1em;
      font-size: 1rem;
      cursor: pointer;
      transition: background 0.15s;
    }
    .editor-toolbar button:hover {
      background: var(--muted);
    }
    .editor-container {
      background: var(--card);
      border: 1px solid var(--border);
      border-radius: 0 0 var(--radius) var(--radius);
      min-height: 320px;
      padding: 1.5em;
      margin-bottom: 2em;
      position: relative;
      font-size: 1.05rem;
      line-height: 1.7;
      box-shadow: var(--shadow);
      transition: box-shadow 0.2s;
    }
    .editor-container:focus-within {
      box-shadow: 0 0 0 2px var(--ring);
    }
  </style>
</head>
<body>
  <div class="max-w-3xl mx-auto mt-10">
    <div class="editor-toolbar rounded-t-lg">
      <button id="uploadBtn" type="button">
        <i data-lucide="file-plus" class="inline-block align-middle mr-1"></i> 上传Word
      </button>
      <button id="pasteBtn" type="button">
        <i data-lucide="clipboard" class="inline-block align-middle mr-1"></i> 粘贴文本
      </button>
      <span class="ml-auto text-sm text-gray-400">AI富文本编辑器</span>
    </div>
    <div class="editor-container" id="editor" contenteditable="true" spellcheck="true">
      <h2>欢迎使用AI富文本编辑器</h2>
      <p>您可以上传Word文档或粘贴文本，然后选中任意内容进行AI智能编辑。</p>
      <ul>
        <li>支持常规富文本格式</li>
        <li>选中内容后，点击AI编辑按钮进行智能改写</li>
      </ul>
    </div>
    <button id="aiEditBtn" class="ai-btn">AI编辑</button>
    <div id="aiModal" class="ai-modal">
      <div class="modal-header">
        <i data-lucide="sparkles"></i> AI编辑
      </div>
      <textarea id="aiPrompt" rows="3" placeholder="请输入您的AI指令，如：润色这段话、改写为更正式的语气……"></textarea>
      <div class="modal-actions">
        <button id="aiSubmit" class="btn btn-primary">提交</button>
        <button id="aiCancel" class="btn btn-secondary">取消</button>
      </div>
      <div id="aiLoading" style="display:none;margin-top:1em;"><span class="loading-spinner"></span>AI处理中…</div>
      <div id="aiError" style="display:none;color:var(--destructive);margin-top:1em;">AI处理失败，请重试。</div>
    </div>
  </div>
  <input type="file" id="wordInput" accept=".doc,.docx" style="display:none;">
  <script>
    lucide.createIcons();
    // AI编辑按钮浮现逻辑
    const editor = document.getElementById('editor');
    const aiBtn = document.getElementById('aiEditBtn');
    let selectionRange = null;
    editor.addEventListener('mouseup', function(e) {
      setTimeout(() => {
        const sel = window.getSelection();
        if (sel && sel.rangeCount > 0 && !sel.isCollapsed && editor.contains(sel.anchorNode)) {
          selectionRange = sel.getRangeAt(0);
          const rect = selectionRange.getBoundingClientRect();
          const editorRect = editor.getBoundingClientRect();
          aiBtn.style.top = (rect.bottom - editorRect.top + window.scrollY + 8) + 'px';
          aiBtn.style.left = (rect.left - editorRect.left + window.scrollX) + 'px';
          aiBtn.classList.add('visible');
        } else {
          aiBtn.classList.remove('visible');
        }
      }, 10);
    });
    document.addEventListener('mousedown', function(e) {
      if (!aiBtn.contains(e.target) && !editor.contains(e.target)) {
        aiBtn.classList.remove('visible');
      }
    });
    // AI编辑弹窗逻辑
    const aiModal = document.getElementById('aiModal');
    const aiPrompt = document.getElementById('aiPrompt');
    const aiSubmit = document.getElementById('aiSubmit');
    const aiCancel = document.getElementById('aiCancel');
    const aiLoading = document.getElementById('aiLoading');
    const aiError = document.getElementById('aiError');
    aiBtn.addEventListener('click', function() {
      aiModal.classList.add('visible');
      aiPrompt.value = '';
      aiError.style.display = 'none';
      aiLoading.style.display = 'none';
      aiPrompt.focus();
    });
    aiCancel.addEventListener('click', function() {
      aiModal.classList.remove('visible');
    });
    aiSubmit.addEventListener('click', function() {
      if (!aiPrompt.value.trim()) return;
      aiLoading.style.display = 'block';
      aiError.style.display = 'none';
      aiSubmit.disabled = true;
      // 模拟AI接口调用
      setTimeout(() => {
        aiLoading.style.display = 'none';
        aiSubmit.disabled = false;
        aiModal.classList.remove('visible');
        if (selectionRange) {
          // 模拟AI返回内容
          const newText = aiPrompt.value + '（AI已处理）';
          selectionRange.deleteContents();
          const span = document.createElement('span');
          span.className = 'highlighted';
          span.textContent = newText;
          selectionRange.insertNode(span);
          setTimeout(() => {
            span.classList.remove('highlighted');
          }, 2000);
        }
        aiBtn.classList.remove('visible');
      }, 1800);
    });
    // 上传Word逻辑（仅模拟）
    const uploadBtn = document.getElementById('uploadBtn');
    const wordInput = document.getElementById('wordInput');
    uploadBtn.addEventListener('click', () => wordInput.click());
    wordInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        // 这里只做文件名展示，实际需后端解析word
        editor.innerHTML = `<h2>${file.name}</h2><p>（Word内容解析后显示在此）</p>`;
      }
    });
    // 粘贴文本逻辑
    const pasteBtn = document.getElementById('pasteBtn');
    pasteBtn.addEventListener('click', function() {
      navigator.clipboard.readText().then(text => {
        if (text) {
          editor.innerHTML = `<p>${text.replace(/\n/g, '<br>')}</p>`;
        }
      });
    });
  </script>
</body>
</html>
