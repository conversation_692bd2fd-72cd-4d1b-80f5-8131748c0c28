# 用户故事：多模型AI选择器功能实现

## 用户故事详情

**故事编号**: US-AI-003  
**史诗编号**: 1.3  
**故事标题**: 多模型AI选择器 - 智能匹配最佳写作模型  
**优先级**: P1 (高优先级)  
**故事点**: 8  
**迭代**: Sprint 3  
**状态**: 草稿  

### 用户故事
```
作为一个 部门经理
我想要 根据不同的写作场景选择最适合的AI模型
以便于 获得最符合需求的写作建议和质量
```

### 场景示例
```gherkin
场景: 项目计划书撰写
  假设 我需要撰写一份详细的项目计划书
  当 我选择GPT-4模型进行内容生成
  那么 AI应该提供结构清晰、逻辑严谨的内容框架
  而且 包含项目管理的最佳实践建议

场景: 日常邮件快速回复
  假设 我需要快速回复一封简单的商务邮件
  当 我选择GPT-3.5-turbo模型
  那么 AI应该提供简洁明了的回复建议
  而且 响应时间应该在1秒以内

场景: 中文内容优化
  假设 我需要优化一份中文商务文档
  当 我选择Kimi-k2-0711-preview模型
  那么 AI应该提供更符合中文表达习惯的优化建议
  而且 保持专业商务语气
```

## 技术实现要求

### 技术架构
基于现有US-AI-001和US-002的已实现架构进行扩展：

**前端架构**:
- **框架**: Vue 3.5.18 + TypeScript 5.8.3 (已集成)
- **UI组件**: Element Plus 2.10.7 (已集成)
- **状态管理**: Pinia (需要实现完整stores)
- **富文本**: WangEditor v5 (已集成)

**AI服务集成**:
- **服务**: api.agicto.cn/v1 (已集成)
- **模型**: 支持5个模型的动态选择和切换
- **缓存**: 模型响应结果缓存机制

### 功能实现

#### 1. 模型选择器组件
**组件**: `ModelSelector.vue`  
**位置**: `src/views/ai-rich-editor/components/`  

```typescript
// 模型配置接口
interface AIModel {
  id: string
  name: string
  description: string
  category: 'speed' | 'quality' | 'chinese' | 'multilingual'
  responseTime: string
  qualityRating: number
  bestFor: string[]
  isActive: boolean
}

// 模型列表配置
const AI_MODELS: AIModel[] = [
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    description: '快速响应，适合日常写作',
    category: 'speed',
    responseTime: '<1秒',
    qualityRating: 3.5,
    bestFor: ['日常邮件', '简单回复', '快速草稿'],
    isActive: true
  },
  {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    description: '高质量输出，适合专业内容',
    category: 'quality',
    responseTime: '2-3秒',
    qualityRating: 5.0,
    bestFor: ['项目计划', '技术文档', '商务提案'],
    isActive: true
  },
  {
    id: 'kimi-k2-0711-preview',
    name: 'Kimi K2',
    description: '中文优化，本土化表达',
    category: 'chinese',
    responseTime: '1-2秒',
    qualityRating: 4.5,
    bestFor: ['中文内容', '本土化文档', '商务中文'],
    isActive: true
  },
  {
    id: 'wenxin-yiyan',
    name: '文心一言',
    description: '百度AI，中文商务场景',
    category: 'chinese',
    responseTime: '2-3秒',
    qualityRating: 4.2,
    bestFor: ['中文商务', '本土化内容', '营销文案'],
    isActive: true
  },
  {
    id: 'qwen-turbo',
    name: '通义千问',
    description: '阿里AI，多语言支持',
    category: 'multilingual',
    responseTime: '2-3秒',
    qualityRating: 4.0,
    bestFor: ['多语言内容', '翻译场景', '国际化文档'],
    isActive: true
  }
]
```

#### 2. 模型状态管理
**文件**: `src/views/ai-rich-editor/stores/ai.ts`

```typescript
import { defineStore } from 'pinia'
import type { AIModel } from '@/types/ai.types'

interface AIState {
  currentModel: string
  availableModels: AIModel[]
  modelPreferences: Record<string, string>
  lastUsedModels: string[]
  performanceMetrics: Record<string, {
    avgResponseTime: number
    successRate: number
    lastUsed: Date
  }>
}

export const useAIStore = defineStore('ai', {
  state: (): AIState => ({
    currentModel: 'gpt-3.5-turbo',
    availableModels: AI_MODELS,
    modelPreferences: {},
    lastUsedModels: [],
    performanceMetrics: {}
  }),

  getters: {
    currentModelConfig: (state) => 
      state.availableModels.find(m => m.id === state.currentModel),
    
    recommendedModels: (state) => {
      // 基于使用场景推荐模型
      return (useCase: string) => {
        return state.availableModels.filter(model => 
          model.bestFor.some(keyword => 
            useCase.toLowerCase().includes(keyword.toLowerCase())
          )
        )
      }
    }
  },

  actions: {
    setCurrentModel(modelId: string) {
      this.currentModel = modelId
      this.updateLastUsed(modelId)
    },

    updateModelMetrics(modelId: string, metrics: { responseTime: number; success: boolean }) {
      if (!this.performanceMetrics[modelId]) {
        this.performanceMetrics[modelId] = {
          avgResponseTime: metrics.responseTime,
          successRate: metrics.success ? 100 : 0,
          lastUsed: new Date()
        }
      } else {
        const existing = this.performanceMetrics[modelId]
        existing.avgResponseTime = (existing.avgResponseTime + metrics.responseTime) / 2
        existing.successRate = (existing.successRate + (metrics.success ? 100 : 0)) / 2
        existing.lastUsed = new Date()
      }
    }
  }
})
```

#### 3. 智能推荐系统
**文件**: `src/views/ai-rich-editor/utils/model-recommender.ts`

```typescript
export class ModelRecommender {
  private usagePatterns: Map<string, number> = new Map()

  recommendModel(context: {
    contentType: string
    textLength: number
    urgency: 'high' | 'medium' | 'low'
    qualityRequirement: 'high' | 'medium' | 'low'
  }): string {
    const { contentType, textLength, urgency, qualityRequirement } = context

    // 基于内容类型推荐
    if (contentType.includes('中文') || contentType.includes('中文')) {
      return 'kimi-k2-0711-preview'
    }

    // 基于文本长度和紧急程度
    if (textLength < 100 && urgency === 'high') {
      return 'gpt-3.5-turbo'
    }

    // 基于质量要求
    if (qualityRequirement === 'high') {
      return 'gpt-4-turbo'
    }

    return 'gpt-3.5-turbo' // 默认模型
  }

  learnFromUsage(modelId: string, success: boolean) {
    const key = `${modelId}_${success ? 'success' : 'failure'}`
    this.usagePatterns.set(key, (this.usagePatterns.get(key) || 0) + 1)
  }
}
```

### 4. UI/UX设计

#### 4.1 模型选择器界面
**位置**: 编辑器顶部工具栏右侧

```vue
<!-- ModelSelector.vue -->
<template>
  <div class="model-selector">
    <el-dropdown @command="handleModelChange">
      <el-button type="primary" plain>
        <el-icon><Cpu /></el-icon>
        {{ currentModelName }}
        <el-icon class="el-icon--right"><ArrowDown /></el-icon>
      </el-button>
      
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item 
            v-for="model in categorizedModels" 
            :key="model.id"
            :command="model.id"
            :disabled="!model.isActive"
          >
            <div class="model-option">
              <div class="model-name">{{ model.name }}</div>
              <div class="model-desc">{{ model.description }}</div>
              <div class="model-meta">
                <el-tag size="small" :type="getCategoryType(model.category)">
                  {{ getCategoryLabel(model.category) }}
                </el-tag>
                <span class="response-time">{{ model.responseTime }}</span>
              </div>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 快速推荐 -->
    <el-tooltip content="智能推荐模型" placement="bottom">
      <el-button 
        type="success" 
        plain 
        @click="showRecommendations"
        :loading="isRecommending"
      >
        <el-icon><MagicStick /></el-icon>
      </el-button>
    </el-tooltip>
  </div>
</template>
```

#### 4.2 模型详情面板
**组件**: `ModelDetailsPanel.vue`

```vue
<template>
  <el-drawer
    v-model="showDetails"
    title="AI模型详情"
    direction="rtl"
    size="400px"
  >
    <div class="model-details">
      <div class="current-model">
        <h3>当前模型: {{ currentModelConfig?.name }}</h3>
        <el-progress 
          :percentage="currentModelConfig?.qualityRating * 20" 
          :format="(val) => `质量评分: ${val/20}/5`"
        />
      </div>

      <div class="usage-stats">
        <h4>使用统计</h4>
        <el-statistic 
          title="平均响应时间" 
          :value="avgResponseTime" 
          suffix="ms" 
        />
        <el-statistic 
          title="成功率" 
          :value="successRate" 
          suffix="%" 
        />
      </div>

      <div class="model-comparison">
        <h4>模型对比</h4>
        <el-table :data="modelComparisonData" style="width: 100%">
          <el-table-column prop="name" label="模型" />
          <el-table-column prop="responseTime" label="响应时间" />
          <el-table-column prop="quality" label="质量评分" />
          <el-table-column prop="bestFor" label="适用场景" />
        </el-table>
      </div>
    </div>
  </el-drawer>
</template>
```

## 详细实现步骤

### Step 1: 类型定义和配置 (1小时)
1. 创建AI模型类型定义文件
2. 定义模型配置常量
3. 设置模型性能基准数据

### Step 2: Pinia状态管理 (2小时)
1. 实现完整的`useAIStore` store
2. 添加模型切换逻辑
3. 实现性能指标跟踪
4. 持久化用户偏好设置

### Step 3: 模型选择器组件 (3小时)
1. 创建`ModelSelector.vue`组件
2. 实现下拉菜单和模型展示
3. 添加模型搜索和过滤功能
4. 集成响应式状态管理

### Step 4: 智能推荐系统 (2小时)
1. 实现`ModelRecommender`类
2. 添加使用模式学习
3. 创建推荐算法
4. 集成到现有AI流程

### Step 5: 集成和测试 (2小时)
1. 将模型选择器集成到现有编辑器
2. 更新AI服务调用使用选中模型
3. 添加单元测试
4. 进行端到端测试

## 验收标准

### 功能验收
- [ ] 支持5个AI模型的动态切换
- [ ] 模型选择器UI响应式设计
- [ ] 实时显示模型性能指标
- [ ] 智能推荐系统基于内容类型推荐
- [ ] 用户偏好设置持久化

### 技术验收
- [ ] 模型切换响应时间<500ms
- [ ] 状态管理使用Pinia实现
- [ ] 性能指标实时更新
- [ ] 错误处理和降级策略
- [ ] 单元测试覆盖率>80%

### 用户体验验收
- [ ] 模型选择界面直观易用
- [ ] 响应时间显示准确
- [ ] 推荐准确性>80%
- [ ] 支持键盘快捷键切换模型
- [ ] 移动设备友好

## 开发任务分解

| 任务编号 | 任务描述 | 预估时间 | 依赖关系 |
|---------|----------|----------|----------|
| TASK-001 | 创建AI模型类型定义和配置 | 1小时 | 无 |
| TASK-002 | 实现Pinia状态管理store | 2小时 | TASK-001 |
| TASK-003 | 开发模型选择器UI组件 | 3小时 | TASK-002 |
| TASK-004 | 实现智能推荐算法 | 2小时 | TASK-002 |
| TASK-005 | 集成到现有编辑器 | 2小时 | TASK-003, TASK-004 |
| TASK-006 | 添加单元测试和文档 | 2小时 | TASK-005 |

**总计开发时间**: 12小时

## 与现有功能集成

### 依赖关系
- **US-AI-001**: 智能文本润色功能（已完成85%）
- **US-002**: Word文档导入功能（准备开发）

### 集成点
1. **AI服务调用**: 更新现有AI服务使用选中的模型
2. **UI集成**: 在编辑器顶部工具栏添加模型选择器
3. **状态管理**: 与现有编辑器状态管理集成
4. **错误处理**: 复用现有错误处理机制

## 性能要求
- **模型切换**: <500ms
- **UI渲染**: <100ms
- **状态更新**: <50ms
- **内存使用**: <10MB额外开销

## 后续扩展
- **自定义模型**: 支持用户添加自定义模型
- **模型性能监控**: 实时性能图表
- **A/B测试**: 不同模型效果对比
- **团队协作**: 团队共享模型偏好

---

**创建日期**: 2025-08-24  
**创建者**: Bob (Scrum Master)  
**状态**: 草稿 → 准备开发  
**关联**: US-AI-001, US-002的后续增强功能  
**质量门禁**: 待QA评估