<template>
  <el-dialog
    v-model="visible"
    title="AI智能润色"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="ai-polish-modal"
  >
    <div class="polish-content">
      <!-- 原始文本 -->
      <div class="text-section original-section">
        <div class="section-header">
          <h4>原始文本</h4>
          <span class="text-count">{{ originalText.length }} 字符</span>
        </div>
        <el-input
          v-model="originalText"
          type="textarea"
          :rows="3"
          readonly
          class="original-text"
        />
      </div>

      <!-- 润色风格选择 -->
      <div class="style-section">
        <h4>润色风格</h4>
        <div class="style-options">
          <div
            v-for="style in POLISH_STYLES"
            :key="style.value"
            class="style-option"
            :class="{ active: selectedStyle === style.value }"
            @click="selectedStyle = style.value"
          >
            <el-icon class="style-icon">
              <component :is="getIcon(style.icon)" />
            </el-icon>
            <div class="style-info">
              <div class="style-name">{{ style.label }}</div>
              <div class="style-desc">{{ style.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 自定义指令 -->
      <div class="custom-section">
        <h4>自定义指令（可选）</h4>
        <el-input
          v-model="customPrompt"
          type="textarea"
          :rows="2"
          placeholder="例如：请用更专业的商务语言表达，保持简洁"
          maxlength="200"
          show-word-limit
        />
      </div>

      <!-- AI模型选择 -->
      <div class="model-section">
        <h4>AI模型</h4>
        <el-select v-model="selectedModel" class="model-select">
          <el-option
            v-for="model in availableModels"
            :key="model.id"
            :label="model.name"
            :value="model.id"
          >
            <div class="model-option">
              <span>{{ model.name }}</span>
              <small>{{ model.description }}</small>
            </div>
          </el-option>
        </el-select>
      </div>

      <!-- 处理状态 -->
      <div v-if="isProcessing" class="processing-section">
        <el-progress
          :percentage="processingProgress"
          :indeterminate="true"
          :duration="2"
        />
        <p class="processing-text">
          <el-icon class="is-loading"><Loading /></el-icon>
          AI正在分析并润色文本...
        </p>
      </div>

      <!-- 润色结果 -->
      <div v-if="polishedText" class="text-section result-section">
        <div class="section-header">
          <h4>润色结果</h4>
          <div class="result-actions">
            <el-button
              size="small"
              type="primary"
              text
              @click="handleCopyResult"
            >
              复制
            </el-button>
            <el-button
              size="small"
              type="primary"
              text
              @click="handleCompare"
            >
              对比
            </el-button>
          </div>
        </div>
        <el-input
          v-model="polishedText"
          type="textarea"
          :rows="4"
          readonly
          class="polished-text"
        />
      </div>

      <!-- 错误提示 -->
      <div v-if="error" class="error-section">
        <el-alert
          :title="error"
          type="error"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="isProcessing"
          :disabled="!selectedText.trim()"
          @click="handlePolish"
        >
          {{ isProcessing ? '处理中...' : '开始润色' }}
        </el-button>
        <el-button
          v-if="polishedText && !isProcessing"
          type="success"
          @click="handleApply"
        >
          应用修改
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 文本对比对话框 -->
  <el-dialog
    v-model="compareVisible"
    title="文本对比"
    width="700px"
    append-to-body
  >
    <div class="text-compare">
      <div class="compare-item">
        <h5>原文</h5>
        <div class="compare-text original">{{ originalText }}</div>
      </div>
      <div class="compare-item">
        <h5>润色后</h5>
        <div class="compare-text polished">{{ polishedText }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  Document, 
  Minus, 
  ChatDotRound, 
  Medal, 
  Lightning, 
  Loading
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { watch } from 'vue'
import { aiProvider } from '../utils/ai-provider'
import { POLISH_STYLES } from '../types/ai.types'

interface Props {
  modelValue: boolean
  selectedText: string
}

interface Emits {
  'update:modelValue': [value: boolean]
  'polish-complete': [polishedText: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态管理
const originalText = ref('')
const polishedText = ref('')
const selectedStyle = ref('formal')
const customPrompt = ref('')
const selectedModel = ref('gpt-3.5-turbo')
const isProcessing = ref(false)
const processingProgress = ref(0)
const error = ref('')
const compareVisible = ref(false)

// 可用模型
const availableModels = [
  { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: '快速响应，适合日常写作' },
  { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: '高质量，适合专业内容' },
  { id: 'kimi-k2-0711-preview', name: 'Kimi K2', description: '中文优化，适合本土化内容' }
]

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 图标映射
const getIcon = (iconName: string) => {
  const iconMap: Record<string, any> = {
    Document,
    Minus,
    ChatDotRound,
    Medal,
    Lightning
  }
  return iconMap[iconName] || Document
}

// 初始化
onMounted(() => {
  originalText.value = props.selectedText
})

// 处理文本润色
const handlePolish = async () => {
  if (!originalText.value.trim()) return

  isProcessing.value = true
  error.value = ''
  processingProgress.value = 0

  try {
    const response = await aiProvider.polishText({
      text: originalText.value,
      style: selectedStyle.value,
      customPrompt: customPrompt.value || undefined,
      model: selectedModel.value
    })

    polishedText.value = response.polishedText
    ElMessage.success('润色完成！')
  } catch (err) {
    error.value = err instanceof Error ? err.message : '润色失败，请重试'
    ElMessage.error(error.value)
  } finally {
    isProcessing.value = false
  }
}

// 应用修改
const handleApply = () => {
  emit('polish-complete', polishedText.value)
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  // 重置状态
  setTimeout(() => {
    polishedText.value = ''
    error.value = ''
    customPrompt.value = ''
  }, 300)
}

// 复制结果
const handleCopyResult = async () => {
  try {
    await navigator.clipboard.writeText(polishedText.value)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 显示对比
const handleCompare = () => {
  compareVisible.value = true
}

// 监听文本变化
watch(() => props.selectedText, (newText) => {
  originalText.value = newText
})
</script>

<style scoped>
.ai-polish-modal {
  max-height: 80vh;
  overflow-y: auto;
}

.polish-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.text-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.text-count {
  font-size: 12px;
  color: #909399;
}

.original-text,
.polished-text {
  font-size: 14px;
  line-height: 1.5;
}

.style-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.style-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.style-option {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.style-option:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.style-option.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.style-icon {
  font-size: 20px;
  margin-right: 12px;
  color: #409eff;
}

.style-info {
  flex: 1;
}

.style-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.style-desc {
  font-size: 12px;
  color: #909399;
}

.custom-section h4,
.model-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.model-select {
  width: 100%;
}

.model-option {
  display: flex;
  flex-direction: column;
}

.model-option small {
  color: #909399;
  font-size: 12px;
}

.processing-section {
  text-align: center;
  padding: 20px;
}

.processing-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 12px 0 0 0;
  color: #606266;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.error-section {
  margin-top: 12px;
}

.text-compare {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.compare-item h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.compare-text {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.compare-text.original {
  background-color: #fafafa;
}

.compare-text.polished {
  background-color: #f0f9ff;
}

@media (max-width: 768px) {
  .style-options {
    grid-template-columns: 1fr;
  }
  
  .text-compare {
    grid-template-columns: 1fr;
  }
}
</style>